/* ------------------------------------------------------------------------------
 *
 *  # ECommerce - Customers
 *
 *  Demo JS code for ecommerce_customers.html page
 *
 * ---------------------------------------------------------------------------- */


// Setup module
// ------------------------------

const EcommerceCustomers = function() {


    //
    // Setup module components
    //

    // Datatable
    const _componentDatatable = function() {
        if (!$().DataTable) {
            console.warn('Warning - datatables.min.js is not loaded.');
            return;
        }

        // Initialize
        $('.table-customers').DataTable({
            autoWidth: false,
            columnDefs: [
                {
                    targets: 0,
                    width: 400
                },
                { 
                    orderable: false,
                    width: 16,
                    targets: 6
                },
                {
                    className: 'control',
                    orderable: false,
                    targets: -1
                },
            ],
            order: [[ 0, 'asc' ]],
            dom: '<"datatable-header justify-content-start"f<"ms-sm-auto"l><"ms-sm-3"B>><"datatable-scroll-wrap"t><"datatable-footer"ip>',
            language: {
                search: '<span class="me-3">Filter:</span> <div class="form-control-feedback form-control-feedback-end flex-fill">_INPUT_<div class="form-control-feedback-icon"><i class="ph-magnifying-glass opacity-50"></i></div></div>',
                searchPlaceholder: 'Type to filter...',
                lengthMenu: '<span class="me-3">Show:</span> _MENU_',
                paginate: { 'first': 'First', 'last': 'Last', 'next': document.dir == "rtl" ? '&larr;' : '&rarr;', 'previous': document.dir == "rtl" ? '&rarr;' : '&larr;' }
            },
            lengthMenu: [ 25, 50, 75, 100 ],
            displayLength: 50,
            responsive: {
                details: {
                    type: 'column',
                    target: -1
                }
            },
            buttons: [
                {
                    extend: 'pdfHtml5',
                    text: 'Export list <i class="ph-file-pdf ms-2"></i>',
                    className: 'btn btn-light',
                    orientation: 'landscape',
                    exportOptions: {
                        columns: [ 0, 1, 2, 3, 4, 5 ],
                        stripHtml: true
                    },
                    customize: function (doc) {
                        doc.content[1].table.widths = Array(doc.content[1].table.body[0].length + 1).join('*').split('');
                    }
                }
            ]
        });
    };


    //
    // Return objects assigned to module
    //

    return {
        init: function() {
            _componentDatatable();
        }
    }
}();


// Initialize module
// ------------------------------

document.addEventListener('DOMContentLoaded', function() {
    EcommerceCustomers.init();
});
