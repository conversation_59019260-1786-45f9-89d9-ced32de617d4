<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<script type="text/javascript">
    $(document).ready(function () {
        checkParametersAmount(${mTotalRows});
        let offensiveChartData = [], defensiveChartData = [], goalChartData = [], resultChartData = [], playerChartData = [];

    <c:forEach var="row" items="${mRows}">
        <c:set var="percentage" value="${row.progressPercentage}"/>
        <c:if test="${row.value == -1}">
            <c:set var="percentage" value="${0}"/>
        </c:if>
        <c:set var="color" value="${row.getProgressClassRBG()}"/>
        <c:set var="category" value="${row.eventTypeName}"/>
        <c:if test="${row.tagTypeName != null}">
            <c:set var="category" value="${category}\n${row.tagTypeName}"/>
        </c:if>
        <c:if test="${row.zoneAbbName != null}">
            <c:set var="category" value="${category}\n${row.zoneAbbName}"/>
        </c:if>
        <c:if test="${percentage < 10}">
            <c:set var="percentage" value="${10}"/>
        </c:if>
        offensiveChartData.push({category: "${category}", value: ${percentage}, color: "${color}"});
    </c:forEach>
    <c:forEach var="row" items="${mSufferedRows}">
        <c:set var="percentage" value="${row.progressPercentage}"/>
        <c:if test="${row.value == -1}">
            <c:set var="percentage" value="${0}"/>
        </c:if>
        <c:set var="color" value="${row.getProgressClassRBG()}"/>
        <c:set var="category" value="${row.eventTypeName}"/>
        <c:if test="${row.tagTypeName != null}">
            <c:set var="category" value="${category}\n${row.tagTypeName}"/>
        </c:if>
        <c:if test="${row.zoneAbbName != null}">
            <c:set var="category" value="${category}\n${row.zoneAbbName}"/>
        </c:if>
        <c:if test="${percentage < 10}">
            <c:set var="percentage" value="${10}"/>
        </c:if>
        defensiveChartData.push({category: "${category}", value: ${percentage}, color: "${color}"});
    </c:forEach>

        if (offensiveChartData.length > 0) {
            getTeamOverviewPolarChart("offensive-chartdiv", offensiveChartData);
        }
        if (defensiveChartData.length > 0) {
            getTeamOverviewPolarChart("defensive-chartdiv", defensiveChartData);
        }

        goalChartData.push({category: "<spring:message code="team.overview.goal"/>", value: ${mTeamData.homeScore}});
        goalChartData.push({category: "<spring:message code="team.overview.goal.suffered"/>", value: ${mTeamData.awayScore}});
        getTeamOverviewGoalChart(goalChartData, ${mTeamData.homeScore - mTeamData.awayScore});

        resultChartData.push({category: "<spring:message code="team.overview.wins"/>", value: ${mTeamData.wins}});
        resultChartData.push({category: "<spring:message code="team.overview.draws"/>", value: ${mTeamData.draws}});
        resultChartData.push({category: "<spring:message code="team.overview.losses"/>", value: ${mTeamData.losses}});
        getTeamOverviewResultChart(resultChartData, ${mTeamData.wins + mTeamData.draws + mTeamData.losses});

        playerChartData.push({category: "", male: ${Math.round((mTeamPlayerData.totalNationals / mTeamPlayerData.totalPlayers * 100) * 100) / 100}, maleMax: 100});
        getTeamOverviewPlayerDataChart(playerChartData);

        getTeamOverviewAgeAverageChart(${mTeamPlayerData.barIndex}, ${mTeamPlayerData.average}, ${mTeamPlayerData.max}, ${mTeamPlayerData.min});

        drawField('moduleContainer', pitch);
    <c:if test="${mModulePoints != null}">
        <c:forEach var="position" items="${mModulePoints}">
        drawPointTeam(${position.x}, ${position.y}, '${mTeams.get(mTeamId).htmlColor}');
        </c:forEach>
    </c:if>
    });

    function redirectToOverview(teamId) {
        let index = filtersIndex.indexOf("teamid");
        if (index >= 0) {
            let letter = ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
            if (index > 25) {
                index -= 25;
                letter = "Z" + ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
            }
            localStorage.setItem("dataanalytics/610/" + letter + "/filter-teamid", teamId);

            location.reload();
        }
    }

    function changeSeasonCompetition(seasonId, competitionId) {
        let index = filtersIndex.indexOf("seasonid");
        if (index >= 0) {
            let letter = ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
            if (index > 25) {
                index -= 25;
                letter = "Z" + ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
            }
            localStorage.setItem("dataanalytics/610/" + letter + "/filter-seasonid", seasonId);
        }

        index = filtersIndex.indexOf("competitionid");
        if (index >= 0) {
            let letter = ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
            if (index > 25) {
                index -= 25;
                letter = "Z" + ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
            }
            localStorage.setItem("dataanalytics/610/" + letter + "/filter-competitionid", competitionId);
        }
        location.reload();
    }
</script>
<div class="row">
    <span class="d-none" id="overview-content-empty" isempty="${mRows.isEmpty() && mSufferedRows.isEmpty()}"></span>
    <span class="d-none" id="overview-content-valid" isvalid="${mValid}"></span>

    <div class="col-12">
        <div class="card mb-2">
            <div class="card-body pt-1 pb-4">
                <div class="row">
                    <div class="col-sm-12 col-md-12 col-lg-2">
                        <div class="d-flex flex-column align-items-center justify-content-center">
                            <img height="120" width="120" class="mt-3 image-shadow" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/${mTeamLogo}.png?<%=System.currentTimeMillis()%>" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/unknownxx.png?<%=System.currentTimeMillis()%>'">
                            <h6 class="mt-1 mb-0">${mTeamName}</h6>
                            <div class="col-12 d-flex align-items-center justify-content-center">
                                <img height="20" width="20" class="image-shadow-sm" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/country/${mCountries.get(mTeams.get(mTeamId).countryId).logo}.png?<%=System.currentTimeMillis()%>" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/country/unknown.png?<%=System.currentTimeMillis()%>'">
                                <span class="ms-2 text-uppercase">${mCountries.get(mTeams.get(mTeamId).countryId).getName(mUser.tvLanguage)}</span>
                            </div>
                            <small class="mt-1">${mCompetitions.get(mCompetitionId).getName(mUser.tvLanguage)}</small>
                        </div>
                    </div>
                    <div class="col-sm-12 col-md-12 col-lg-8">
                        <div class="row h-100">
                            <div class="col-sm-12 col-lg-4">
                                <div class="d-flex flex-column h-100">
                                    <h6 class="mb-0 text-center"><spring:message code="team.overview.win.draw.loss"/></h6>
                                    <div id="resultChartContainer" class="d-flex flex-1 align-items-stretch">

                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-12 col-lg-4">
                                <div class="d-flex flex-column h-100">
                                    <h6 class="mb-0 text-center"><spring:message code="team.overview.goal.and.goal.suffered"/></h6>
                                    <div id="goalChartContainer" class="d-flex flex-1 align-items-stretch">

                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-12 col-lg-4">
                                <div class="h-50">
                                    <div class="d-flex flex-column h-100">
                                        <h6 class="mb-0 text-center text-uppercase"><spring:message code="team.overview.players.from"/> ${mCountries.get(mTeams.get(mTeamId).countryId).getName(mUser.tvLanguage)}: ${mTeamPlayerData.totalNationals} / ${mTeamPlayerData.totalPlayers}</h6>
                                        <div id="playerChartContainer" class="d-flex flex-1 align-items-stretch">

                                        </div>
                                    </div>
                                </div>
                                <div class="h-50">
                                    <div class="d-flex flex-column h-100">
                                        <h6 class="mb-0 text-center"><spring:message code="team.overview.age.average"/></h6>
                                        <div id="ageAverageChartContainer" class="d-flex flex-1 align-items-stretch">

                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-12 col-md-12 col-lg-2 d-flex flex-column align-items-center justify-content-center" id="moduleContainer">
                        <h6 class="mb-0 text-center"><spring:message code="team.overview.main.module"/></h6>
                        <p class="mb-0 fw-bold">${mTeamData.mainModule}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-12">
        <div class="card mb-2">
            <div class="card-body">
                <c:if test="${!mRows.isEmpty() || !mSufferedRows.isEmpty()}">
                    <div class="row">
                        <c:if test="${!mRows.isEmpty()}">
                            <div class="col-sm-12 col-md-6 col-lg-6">
                                <h6 class="mb-0 text-center"><spring:message code="team.overview.offensive.metrics"/></h6>
                                <p class="mb-0 text-center text-muted fs-sm">(<spring:message code="team.overview.percentage.values"/>)</p>
                                <div id="offensive-chartdiv" class="d-flex flex-1 align-items-stretch min-vh-50-sm vh-40">

                                </div>
                            </div>
                        </c:if>
                        <c:if test="${!mSufferedRows.isEmpty()}">
                            <div class="col-sm-12 col-md-6 col-lg-6">
                                <h6 class="mb-0 text-center"><spring:message code="team.overview.defensive.metrics"/></h6>
                                <p class="mb-0 text-center text-muted fs-sm">(<spring:message code="team.overview.percentage.values"/>)</p>
                                <div id="defensive-chartdiv" class="d-flex flex-1 align-items-stretch min-vh-50-sm vh-40">

                                </div>
                            </div>
                        </c:if>
                    </div>
                </c:if>
            </div>
        </div>
    </div>

    <div class="col-12">
        <div class="card mb-0">
            <div class="card-body pt-0">
                <div class="table-responsive">
                    <table class="table table-hover" id="overview-table">
                        <thead>
                            <tr>
                                <th><spring:message code="team.overview.kpi"/></th>
                                <th class="text-center"><spring:message code="team.overview.ranking"/></th>
                                <th class="text-center"><spring:message code="team.overview.value"/></th>
                                <th class="text-center py-0" style="width: 35vw;">
                                    <div class="text-center d-flex align-items-center justify-content-center">
                                        <img class="me-1 rounded-pill" width="32" height="32" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/${mTeamLogo}.png?<%=System.currentTimeMillis()%>" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/unknownxx.png?<%=System.currentTimeMillis()%>'"/>
                                        <h6 class="mb-0">${mTeamName.toUpperCase()} <spring:message code="team.overview.overview"/></h6>
                                    </div>
                                </th>
                                <th class="text-center"><spring:message code="team.overview.min.max"/></th>
                                <th><spring:message code="team.overview.competition.average"/></th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="7" class="text-center p-1">
                                    <h6 class="mb-0"><spring:message code="team.overview.offensive.metrics"/> (${mOffensiveIndexAverage}�)</h6>
                                </td>
                            </tr>
                            <c:forEach var="row" items="${mRows}">
                                <tr>
                                    <td class="p-1">
                                        ${row.eventTypeName}
                                        <c:if test="${row.tagTypeName != null}">
                                            <span class="fs-sm">${row.tagTypeName}</span>
                                        </c:if>
                                        <c:if test="${row.zoneAbbName != null}">
                                            <span class="fs-sm text-decoration-underline" title="${row.zoneName}">${row.zoneAbbName}</span>
                                        </c:if>
                                    </td>
                                    <td class="text-center p-1">
                                        ${row.index}�
                                    </td>
                                    <td class="text-center p-1">
                                        <c:choose>
                                            <c:when test="${row.value == -1}">
                                                -
                                            </c:when>
                                            <c:when test="${mDisplayType.equals('TOTALS')}">
                                                <c:choose>
                                                    <c:when test="${mAdvancedMetrics.containsKey(row.eventTypeId)}">
                                                        ${row.value}
                                                    </c:when>
                                                    <c:otherwise>
                                                        ${Math.round(row.value)}
                                                    </c:otherwise>
                                                </c:choose>
                                            </c:when>
                                            <c:otherwise>
                                                ${row.value}
                                            </c:otherwise>
                                        </c:choose>
                                    </td>
                                    <td class="p-1">
                                        <div class="progress">
                                            <c:set var="coloredPerc" value="${row.progressPercentage}"/>
                                            <c:set var="coloredPercText" value="${row.progressPercentage}%"/>
                                            <c:set var="averagePerc" value="${Math.round((row.average - row.minValue) / (row.maxValue - row.minValue) * 100)}"/>
                                            <c:if test="${row.isOpposite != null && row.isOpposite}">
                                                <c:set var="averagePerc" value="${100 - averagePerc}"/>
                                            </c:if>
                                            <c:set var="averagePerc" value="${averagePerc - row.progressPercentage}"/>
                                            <c:set var="averagePerc" value="${averagePerc}%"/>

                                            <c:if test="${row.value != -1}">
                                                <div class="progress-bar progress-bar-striped bg-${row.getProgressClass()} rounded-end" style="width: ${coloredPercText}" aria-valuenow="${coloredPerc}" aria-valuemin="${row.minValue}" aria-valuemax="${row.maxValue}"></div>
                                                <div style="margin-left: ${averagePerc}; width: 1px; transform: translateX(-50%); border-left: 1px dashed #000;"></div>
                                                <!--<img class="rounded-pill" width="20" height="20" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/players/${mPlayerPhoto}.png?<%=System.currentTimeMillis()%>" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/players/unknownxx.png?<%=System.currentTimeMillis()%>'"/>-->
                                            </c:if>
                                        </div>
                                    </td>
                                    <c:choose>
                                        <c:when test="${mDisplayType.equals('TOTALS')}">
                                            <td class="text-center text-decoration-underline p-1" data-bs-popup="popover" data-bs-trigger="hover" data-bs-content="<c:if test="${row.isOpposite == null || !row.isOpposite}">${row.minValueTeam} / ${row.maxValueTeam}</c:if><c:if test="${row.isOpposite != null && row.isOpposite}">${row.maxValueTeam} / ${row.minValueTeam}</c:if>" data-bs-original-title="<spring:message code="team.overview.worst.best"/>" data-bs-placement="top">
                                                <c:choose>
                                                    <c:when test="${mAdvancedMetrics.containsKey(row.eventTypeId)}">
                                                        ${row.minValue} / ${row.maxValue}
                                                    </c:when>
                                                    <c:otherwise>
                                                        ${Math.round(row.minValue)} / ${Math.round(row.maxValue)}
                                                    </c:otherwise>
                                                </c:choose>
                                            </td>
                                        </c:when>
                                        <c:otherwise>
                                            <td class="text-center text-decoration-underline p-1" data-bs-popup="popover" data-bs-trigger="hover" data-bs-content="<c:if test="${row.isOpposite == null || !row.isOpposite}">${row.minValueTeam} / ${row.maxValueTeam}</c:if><c:if test="${row.isOpposite != null && row.isOpposite}">${row.maxValueTeam} / ${row.minValueTeam}</c:if>" data-bs-original-title="<spring:message code="team.overview.worst.best"/>" data-bs-placement="top">
                                                ${row.minValue} / ${row.maxValue}
                                            </td>
                                        </c:otherwise>
                                    </c:choose>
                                    <td class="text-center p-1">
                                        ${row.average}
                                    </td>
                                </tr>
                            </c:forEach>
                            <tr>
                                <td colspan="7" class="text-center p-1">
                                    <h6 class="mb-0"><spring:message code="team.overview.defensive.metrics"/> (${mDefensiveIndexAverage}�)</h6>
                                </td>
                            </tr>
                            <c:forEach var="row" items="${mSufferedRows}">
                                <tr>
                                    <td class="p-1">
                                        ${row.eventTypeName}
                                        <c:if test="${row.tagTypeName != null}">
                                            <span class="fs-sm">${row.tagTypeName}</span>
                                        </c:if>
                                        <c:if test="${row.zoneAbbName != null}">
                                            <span class="fs-sm text-decoration-underline" title="${row.zoneName}">${row.zoneAbbName}</span>
                                        </c:if>
                                    </td>
                                    <td class="text-center p-1">
                                        ${row.index}�
                                    </td>
                                    <td class="text-center p-1">
                                        <c:choose>
                                            <c:when test="${row.value == -1}">
                                                -
                                            </c:when>
                                            <c:when test="${mDisplayType.equals('TOTALS')}">
                                                <c:choose>
                                                    <c:when test="${mAdvancedMetrics.containsKey(row.eventTypeId)}">
                                                        ${row.value}
                                                    </c:when>
                                                    <c:otherwise>
                                                        ${Math.round(row.value)}
                                                    </c:otherwise>
                                                </c:choose>
                                            </c:when>
                                            <c:otherwise>
                                                ${row.value}
                                            </c:otherwise>
                                        </c:choose>
                                    </td>
                                    <td class="p-1">
                                        <div class="progress">
                                            <c:set var="coloredPerc" value="${row.progressPercentage}"/>
                                            <c:set var="coloredPercText" value="${row.progressPercentage}%"/>
                                            <c:set var="averagePerc" value="${Math.round((row.average - row.minValue) / (row.maxValue - row.minValue) * 100)}"/>
                                            <c:if test="${row.isOpposite != null && row.isOpposite}">
                                                <c:set var="averagePerc" value="${100 - averagePerc}"/>
                                            </c:if>
                                            <c:set var="averagePerc" value="${averagePerc - row.progressPercentage}"/>
                                            <c:set var="averagePerc" value="${averagePerc}%"/>

                                            <c:if test="${row.value != -1}">
                                                <div class="progress-bar progress-bar-striped bg-${row.getProgressClass()} rounded-end" style="width: ${coloredPercText}" aria-valuenow="${coloredPerc}" aria-valuemin="${row.minValue}" aria-valuemax="${row.maxValue}"></div>
                                                <div style="margin-left: ${averagePerc}; width: 1px; transform: translateX(-50%); border-left: 1px dashed #000;"></div>
                                                <!--<img class="rounded-pill" width="20" height="20" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/players/${mPlayerPhoto}.png?<%=System.currentTimeMillis()%>" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/players/unknownxx.png?<%=System.currentTimeMillis()%>'"/>-->
                                            </c:if>
                                        </div>
                                    </td>
                                    <c:choose>
                                        <c:when test="${mDisplayType.equals('TOTALS')}">
                                            <td class="text-center text-decoration-underline p-1" data-bs-popup="popover" data-bs-trigger="hover" data-bs-content="<c:if test="${row.isOpposite == null || !row.isOpposite}">${row.minValueTeam} / ${row.maxValueTeam}</c:if><c:if test="${row.isOpposite != null && row.isOpposite}">${row.maxValueTeam} / ${row.minValueTeam}</c:if>" data-bs-original-title="<spring:message code="team.overview.worst.best"/>" data-bs-placement="top">
                                                <c:choose>
                                                    <c:when test="${mAdvancedMetrics.containsKey(row.eventTypeId)}">
                                                        ${row.minValue} / ${row.maxValue}
                                                    </c:when>
                                                    <c:otherwise>
                                                        ${Math.round(row.minValue)} / ${Math.round(row.maxValue)}
                                                    </c:otherwise>
                                                </c:choose>
                                            </td>
                                        </c:when>
                                        <c:otherwise>
                                            <td class="text-center text-decoration-underline p-1" data-bs-popup="popover" data-bs-trigger="hover" data-bs-content="<c:if test="${row.isOpposite == null || !row.isOpposite}">${row.minValueTeam} / ${row.maxValueTeam}</c:if><c:if test="${row.isOpposite != null && row.isOpposite}">${row.maxValueTeam} / ${row.minValueTeam}</c:if>" data-bs-original-title="<spring:message code="team.overview.worst.best"/>" data-bs-placement="top">
                                                ${row.minValue} / ${row.maxValue}
                                            </td>
                                        </c:otherwise>
                                    </c:choose>
                                    <td class="text-center p-1">
                                        ${row.average}
                                    </td>
                                </tr>
                            </c:forEach>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="col-12">
        <div class="row mt-2">
            <c:if test="${!mTeamLastFixtures.isEmpty()}">
                <div class="col-sm-12 col-md-12 col-lg-8">
                    <div class="card mb-0 h-100">
                        <div class="card-body">
                            <h6 class="mb-0 text-center"><spring:message code="team.overview.last.team.matches"/></h6>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th class="text-center"><spring:message code="team.overview.match"/></th>
                                            <th class="text-center"><spring:message code="team.overview.date"/></th>
                                            <th class="text-center"><spring:message code="team.overview.xg"/></th>
                                            <th class="text-center"><spring:message code="team.overview.opponent.xg"/></th>
                                            <th class="text-center"><spring:message code="team.overview.possession"/></th>
                                            <th></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <c:forEach var="row" items="${mTeamLastFixtures}">
                                            <c:set var="extraClass" value="bg-yellow text-dark-yellow"/>
                                            <c:if test="${row.homeTeamId == mTeamId}">
                                                <c:if test="${row.homeScore > row.awayScore}">
                                                    <c:set var="extraClass" value="bg-success text-dark-success"/>
                                                </c:if>
                                                <c:if test="${row.homeScore < row.awayScore}">
                                                    <c:set var="extraClass" value="bg-danger text-dark-danger"/>
                                                </c:if>
                                            </c:if>
                                            <c:if test="${row.awayTeamId == mTeamId}">
                                                <c:if test="${row.homeScore > row.awayScore}">
                                                    <c:set var="extraClass" value="bg-danger text-dark-danger"/>
                                                </c:if>
                                                <c:if test="${row.homeScore < row.awayScore}">
                                                    <c:set var="extraClass" value="bg-success text-dark-success"/>
                                                </c:if>
                                            </c:if>
                                            <tr class="fs-sm">
                                                <td class="text-center px-1">
                                                    <table class="w-100">
                                                        <tbody>
                                                            <tr>
                                                                <td class="text-end fs-sm" style="width: 35%">
                                                                    ${mTeams.get(row.homeTeamId).getName(mUser.tvLanguage)}
                                                                </td>
                                                                <td class="text-center" style="width: 10%">
                                                                    <img width="24" height="24" class="rounded-pill cursor-pointer" onclick="redirectToOverview(${row.homeTeamId});" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/${mTeams.get(row.homeTeamId).logo}.png?<%=System.currentTimeMillis()%>" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/unknownxx.png?<%=System.currentTimeMillis()%>'"/>
                                                                </td>
                                                                <td class="text-center" style="width: 10%">
                                                                    <span class="text-muted fs-sm">${row.homeScore}-${row.awayScore}</span>
                                                                </td>
                                                                <td class="text-center" style="width: 10%">
                                                                    <img width="24" height="24" class="rounded-pill cursor-pointer" onclick="redirectToOverview(${row.awayTeamId});" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/${mTeams.get(row.awayTeamId).logo}.png?<%=System.currentTimeMillis()%>" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/unknownxx.png?<%=System.currentTimeMillis()%>'"/>
                                                                </td>
                                                                <td class="fs-sm" style="width: 35%">
                                                                    ${mTeams.get(row.awayTeamId).getName(mUser.tvLanguage)}
                                                                </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </td>
                                                <td class="text-center px-1">
                                                    ${row.getGameDateString()}
                                                </td>
                                                <td class="text-center px-1">
                                                    ${Math.round(row.homexG * 100) / 100}
                                                </td>
                                                <td class="text-center px-1">
                                                    ${Math.round(row.awayxG * 100) / 100}
                                                </td>
                                                <td class="text-center px-1">
                                                    <c:if test="${row.homeTeamId == mTeamId}">
                                                        ${row.homePossesso}%
                                                    </c:if>
                                                    <c:if test="${row.awayTeamId == mTeamId}">
                                                        ${row.awayPossesso}%
                                                    </c:if>
                                                </td>
                                                <td>
                                                    <span class="d-inline-block p-1 fw-bold rounded-2 bg-opacity-75 ${extraClass}">
                                                        <c:if test="${row.homeScore == row.awayScore}">
                                                            <spring:message code="team.overview.d"/>
                                                        </c:if>
                                                        <c:if test="${row.homeTeamId == mTeamId}">
                                                            <c:if test="${row.homeScore > row.awayScore}">
                                                                <spring:message code="team.overview.w"/>
                                                            </c:if>
                                                            <c:if test="${row.homeScore < row.awayScore}">
                                                                <spring:message code="team.overview.l"/>
                                                            </c:if>
                                                        </c:if>
                                                        <c:if test="${row.awayTeamId == mTeamId}">
                                                            <c:if test="${row.homeScore > row.awayScore}">
                                                                <spring:message code="team.overview.l"/>
                                                            </c:if>
                                                            <c:if test="${row.homeScore < row.awayScore}">
                                                                <spring:message code="team.overview.w"/>
                                                            </c:if>
                                                        </c:if>
                                                    </span>
                                                </td>
                                            </tr>
                                        </c:forEach>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </c:if>
            <c:if test="${!mTeamCareer.isEmpty()}">
                <div class="col-sm-12 col-md-12 col-lg-4 mt-2 mt-sm-2 mt-md-2 mt-lg-0 ps-lg-0">
                    <div class="card mb-0 h-100">
                        <div class="card-body">
                            <h6 class="mb-0 text-center"><spring:message code="team.overview.team.career"/></h6>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th><spring:message code="team.overview.season"/></th>
                                            <th class="text-center"><spring:message code="team.overview.competition"/></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <c:forEach var="row" items="${mTeamCareer}">
                                            <c:set var="onClick" value=""/>
                                            <c:if test="${row.clickable}">
                                                <c:set var="onClick" value="changeSeasonCompetition(${row.seasonId}, ${row.competitionId});"/>
                                            </c:if>
                                            <tr class="${row.clickable ? '' : 'opacity-50'}">
                                                <td class="px-1">
                                                    ${row.season.name}
                                                </td>
                                                <td class="p-1">
                                                    <img class="rounded-pill ${row.clickable ? 'cursor-pointer' : ''}" onclick="${onClick}" width="32" height="32" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/competitionslogo/${row.competition.logo}.png?<%=System.currentTimeMillis()%>" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/competitionslogo/unknown.png?<%=System.currentTimeMillis()%>'"/>
                                                    ${row.competition.getName(mUser.tvLanguage)}
                                                </td>
                                            </tr>
                                        </c:forEach>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </c:if>
        </div>
    </div>
</div>