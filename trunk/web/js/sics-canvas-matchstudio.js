var pitch, scale, scaleSvg, multiplier;
var tip;
var currentContainerId;
var raggio = 7.0;
var basePitchWidth = 138.6, basePitchLength = 89.76;
var realWidth = 105, realLength = 68;
var minDistance;
var zoneEventAmount = new Map(), angleEventAmount = new Map();
var eventMap = new Map();
var pitchCounter = 0;

var lineColors = {
    black: "#2a2d2a",
    blue: "#3672ff",
    red: "#ff4545"
};

pitch = {
    width: basePitchWidth,
    length: basePitchLength,
    padding: {
        top: 1,
        right: 1,
        bottom: 1,
        left: 1
    },
    paintColor: "#FFF",
    grassColor: "#40b36d",
    isDefault: true,
    id: "basePitch"
};

var basePitchVerticalWidth = 68, basePitchVerticalLength = 105;
var pitchVertical = {
    width: basePitchVerticalWidth / 2.5,
    length: basePitchVerticalLength / 2.5,
    padding: {
        top: 0.5,
        right: 0.5,
        bottom: 0.5,
        left: 0.5
    },
    paintColor: "#FFF",
    grassColor: "#40b36d",
    isDefault: true,
    isVertical: true,
    id: "basePitchVertical"
};

// Cache for baseLayer SVG groups, keyed by 'width;height'
var baseLayerCache = new Map();

function drawField(containerId, pitchElement) {
    currentContainerId = containerId;

    if (typeof pitchElement === 'undefined' || !pitchElement) {
        pitchElement = pitch;
    }

    var tmpMultiplier = pitchElement.length / realLength;
    var tmpScale = d3.scaleLinear()
            .domain([0, 100])
            .range([0, 500 * tmpMultiplier]);

    if (pitchElement.isDefault) {
        multiplier = tmpMultiplier;
        scale = tmpScale;

        minDistance = scale(2.3); // distanza dipendente dalla dimensione del field
    }

    scaleSvg = d3.scaleLinear()
            .domain([0, 100])
            .range([0, 500]);

    var baseBezier = 50 * tmpMultiplier;
    pitchElement.id += pitchCounter;
    pitchCounter++;

    // rimuovo precedente se esistente
    d3.select("#" + pitchElement.id).remove();
    var svgWidth = scaleSvg(pitchElement.width + pitchElement.padding.left + pitchElement.padding.right);
    var svgHeight = scaleSvg(pitchElement.length + pitchElement.padding.top + pitchElement.padding.bottom);
    var svg = d3.select("#" + currentContainerId).append("svg")
            .attr("width", svgWidth)
            .attr("height", svgHeight)
            .attr("style", "background:" + pitchElement.grassColor)
            .attr("id", pitchElement.id);

    var cacheKey = svgWidth + ";" + svgHeight;
    var baseLayer;
    if (baseLayerCache.has(cacheKey)) {
        // Use cached baseLayer
        var cachedNode = baseLayerCache.get(cacheKey).cloneNode(true);
        baseLayer = d3.select(svg.node().appendChild(cachedNode));
    } else {
        // Create new baseLayer and cache it
        baseLayer = svg.append("g")
            .attr("data-index", "0")
            .attr("transform", "translate(" + scaleSvg(pitchElement.padding.left) + "," + scaleSvg(pitchElement.padding.top) + ")");

        // fasce alternate verde chiaro e verde scuro
        var stripeHeight = pitchElement.width / 8; // Altezza delle fasce
        var numStripes = Math.floor(scaleSvg(pitchElement.width) / scaleSvg(stripeHeight));

        if (pitchElement.isDefault) {
            for (var i = 0; i < numStripes; i++) {
                var stripeColor = i % 2 === 0 ? "#46ac6d" : "#40b36d"; // Alterna tra verde chiaro e scuro
                baseLayer.append("rect")
                        .attr("x", scaleSvg(i * stripeHeight))
                        .attr("y", 0)
                        .attr("width", scaleSvg(stripeHeight))
                        .attr("height", scaleSvg(pitchElement.length))
                        .attr("fill", stripeColor);
            }
        }

        // linee dei campi
        baseLayer.append("rect")
                .attr("x", 0)
                .attr("y", 0)
                .attr("width", scaleSvg(pitchElement.width))
                .attr("height", scaleSvg(pitchElement.length))
                .attr("stroke", pitchElement.paintColor)
                .attr("fill", "none");

        baseLayer.append("circle")
                .attr("cx", scaleSvg(pitchElement.width / 2))
                .attr("cy", scaleSvg(pitchElement.length / 2))
                .attr("r", 2)
                .attr("fill", pitchElement.paintColor);

        baseLayer.append("circle")
                .attr("cx", scaleSvg(pitchElement.width / 2))
                .attr("cy", scaleSvg(pitchElement.length / 2))
                .attr("r", baseBezier)
                .attr("fill", 'none')
                .attr("stroke", pitchElement.paintColor);

        baseLayer.append("line")
                .attr("y1", scaleSvg(pitchElement.length))
                .attr("y2", 0)
                .attr("x1", scaleSvg(pitchElement.width / 2))
                .attr("x2", scaleSvg(pitchElement.width / 2))
                .attr("stroke", pitchElement.paintColor);

        // corners
        function addPath(pathData, parentElement, size) {
            var path = parentElement.append("path")
                    .attr("d", pathData)
                    .attr("stroke", pitchElement.paintColor)
                    .attr("fill", "none");

            if (typeof size !== 'undefined') {
                path.attr("stroke-width", size);
            }

            return path;
        }

        // top left
        var pathData = "M0," + scaleSvg(1) + "A " + scaleSvg(1) + " " + scaleSvg(1) + " 45 0 0" + scaleSvg(1) + ",0";
        addPath(pathData, baseLayer);

        // top right
        pathData = "M" + scaleSvg(pitchElement.width - 1) + ",0 A " + scaleSvg(1) + " " + scaleSvg(1) + " 45 0 0" + scaleSvg(pitchElement.width) + "," + scaleSvg(1);
        addPath(pathData, baseLayer);

        // bottom left
        pathData = "M0," + scaleSvg(pitchElement.length - 1) + "A " + scaleSvg(1) + " " + scaleSvg(1) + " 45 0 1" + scaleSvg(1) + "," + scaleSvg(pitchElement.length);
        addPath(pathData, baseLayer);

        // top right
        pathData = "M" + scaleSvg(pitchElement.width - 1) + "," + scaleSvg(pitchElement.length) + " A " + scaleSvg(1) + " " + scaleSvg(1) + " 45 0 1" + scaleSvg(pitchElement.width) + "," + scaleSvg(pitchElement.length - 1);
        addPath(pathData, baseLayer);

        // Top Penalty Area
        var penaltyAreaTop = baseLayer.append("g");
        pathData = "M0," + tmpScale(14) + "L" + tmpScale(16.5) + "," + tmpScale(14) + "L" + tmpScale(16.5) + "," + tmpScale(54) + "L0," + tmpScale(54);
        addPath(pathData, penaltyAreaTop);

        // Top Penalty Area
        pathData = "M0," + tmpScale(24.84) + "L" + tmpScale(5.5) + "," + tmpScale(24.84) + "L" + tmpScale(5.5) + "," + tmpScale(43.16) + "L0," + tmpScale(43.16);
        addPath(pathData, penaltyAreaTop);

        // Top D
        pathData = "M" + tmpScale(16.5) + "," + tmpScale(42) + "A " + baseBezier + " " + baseBezier + " 5 0 0 " + tmpScale(16.5) + "," + tmpScale(26);
        addPath(pathData, penaltyAreaTop);

        // Top Penalty Spot
        penaltyAreaTop.append("circle")
                .attr("cx", tmpScale(11))
                .attr("cy", tmpScale(34))
                .attr("r", 1)
                .attr("fill", pitchElement.paintColor)
                .attr("stroke", pitchElement.paintColor);

        var penaltyAreaBottom = baseLayer.append("g");
        penaltyAreaBottom.html(penaltyAreaTop.html());
        penaltyAreaBottom.attr("transform", "rotate(180) translate(-" + scaleSvg(pitchElement.width) + ",-" + scaleSvg(pitchElement.length) + ")");

        // Cache a clone of the baseLayer node
        baseLayerCache.set(cacheKey, baseLayer.node().cloneNode(true));
    }

    pitchElement.baseLayer = baseLayer;
    if (pitchElement.isDefault) {
        var pointLayer = svg.append("g")
                .attr("data-index", "1")
                .attr("transform", "translate(" + scaleSvg(pitchElement.padding.left) + "," + scaleSvg(pitchElement.padding.top) + ")");
        pitchElement.pointLayer = pointLayer;

        tip = d3.tip()
                .attr('class', 'd3-tip')
                .offset([-10, 0])
                .html(function (d) {
                    return d;
                });
    }
}

function drawVerticalField(containerId, pitchElement, settings) {
    currentContainerId = containerId;

    if (typeof pitchElement === 'undefined' || !pitchElement) {
        pitchElement = pitch;
    }

    var tmpMultiplier = pitchElement.length / realWidth;
    var tmpScale = d3.scaleLinear()
        .domain([0, 100])
        .range([0, 500 * tmpMultiplier]);

    if (pitchElement.isDefault) {
        multiplier = tmpMultiplier;
        scale = tmpScale;

        minDistance = scale(2.3); // distanza dipendente dalla dimensione del field
    }

    scaleSvg = d3.scaleLinear()
        .domain([0, 100])
        .range([0, 500]);

    var baseBezier = 50 * tmpMultiplier;
    pitchElement.id += pitchCounter;
    pitchCounter++;

    // rimuovo precedente se esistente
    d3.select("#" + pitchElement.id).remove();
    var svgWidth = scaleSvg(pitchElement.width + pitchElement.padding.left + pitchElement.padding.right);
    var svgHeight = scaleSvg(pitchElement.length + pitchElement.padding.top + pitchElement.padding.bottom);
    var svg = d3.select("#" + currentContainerId).append("svg")
        .attr("width", svgWidth)
        .attr("height", svgHeight)
        .attr("style", "background:" + pitchElement.grassColor)
        .attr("id", pitchElement.id);

    var cacheKey = svgWidth + ";" + svgHeight;
    var baseLayer;
    if (baseLayerCache.has(cacheKey)) {
        // Use cached baseLayer
        var cachedNode = baseLayerCache.get(cacheKey).cloneNode(true);
        baseLayer = d3.select(svg.node().appendChild(cachedNode));
    } else {
        // Create new baseLayer and cache it
        baseLayer = svg.append("g")
            .attr("data-index", "0")
            .attr("transform", "translate(" + scaleSvg(pitchElement.padding.left) + "," + scaleSvg(pitchElement.padding.top) + ")");

        // fasce alternate verde chiaro e verde scuro
        var stripeHeight = pitchElement.length / 8; // Altezza delle fasce
        var numStripes = Math.floor(scaleSvg(pitchElement.length) / scaleSvg(stripeHeight));

        if (pitchElement.isDefault) {
            for (var i = 0; i < numStripes; i++) {
                var stripeColor = i % 2 === 0 ? "#46ac6d" : "#40b36d"; // Alterna tra verde chiaro e scuro
                baseLayer.append("rect")
                    .attr("x", 0)
                    .attr("y", scaleSvg(i * stripeHeight))
                    .attr("width", scaleSvg(pitchElement.width))
                    .attr("height", scaleSvg(stripeHeight))
                    .attr("fill", stripeColor);
            }
        }

        // linee dei campi
        baseLayer.append("rect")
            .attr("x", 0)
            .attr("y", 0)
            .attr("width", scaleSvg(pitchElement.width))
            .attr("height", scaleSvg(pitchElement.length))
            .attr("stroke", pitchElement.paintColor)
            .attr("fill", "none");

        var skipMidfieldLines = settings && settings.skipMidfieldLines;
        if (!skipMidfieldLines) {
            baseLayer.append("circle")
                .attr("cx", scaleSvg(pitchElement.width / 2))
                .attr("cy", scaleSvg(pitchElement.length / 2))
                .attr("r", 2)
                .attr("fill", pitchElement.paintColor);

            baseLayer.append("circle")
                .attr("cx", scaleSvg(pitchElement.width / 2))
                .attr("cy", scaleSvg(pitchElement.length / 2))
                .attr("r", baseBezier)
                .attr("fill", 'none')
                .attr("stroke", pitchElement.paintColor);

            baseLayer.append("line")
                .attr("y1", scaleSvg(pitchElement.length / 2))
                .attr("y2", scaleSvg(pitchElement.length / 2))
                .attr("x1", scaleSvg(pitchElement.width))
                .attr("x2", 0)
                .attr("stroke", pitchElement.paintColor);
        }

        if (settings && settings.drawChannelsAndThirds) {
            // Terzo Offensivo
            baseLayer.append("line")
                .attr("y1", scaleSvg(pitchElement.length / 3))
                .attr("y2", scaleSvg(pitchElement.length / 3))
                .attr("x1", scaleSvg(pitchElement.width))
                .attr("x2", 0)
                .attr("stroke-dasharray", "2,2")
                .attr("stroke", pitchElement.paintColor);

            // Terzo Difensivo
            baseLayer.append("line")
                .attr("y1", scaleSvg(pitchElement.length / 3 * 2))
                .attr("y2", scaleSvg(pitchElement.length / 3 * 2))
                .attr("x1", scaleSvg(pitchElement.width))
                .attr("x2", 0)
                .attr("stroke-dasharray", "2,2")
                .attr("stroke", pitchElement.paintColor);

            for (var i = 1; i < 5; i++) {
                baseLayer.append("line")
                    .attr("y1", scaleSvg(pitchElement.length))
                    .attr("y2", 0)
                    .attr("x1", scaleSvg(pitchElement.width / 5 * i))
                    .attr("x2", scaleSvg(pitchElement.width / 5 * i))
                    .attr("stroke-dasharray", "2,2")
                    .attr("stroke", pitchElement.paintColor);

                baseLayer.append("text")
                    .attr("x", function () {
                        return scaleSvg(pitchElement.width / 5 * i) - scaleSvg(pitchElement.width / 5 / 2);
                    })
                    .attr("y", function () {
                        return scaleSvg(1.75);
                    })  // Regola la posizione del testo verticalmente
                    .attr("text-anchor", "middle")  // Centra il testo rispetto al cerchio
                    .attr("fill", "white")  // Colore del testo
                    .attr("font-size", calculateTextSize(20))
                    .text(function () {
                        return "C" + i;
                    });
            }

            // testo C5
            baseLayer.append("text")
                .attr("x", function () {
                    return scaleSvg(pitchElement.width / 5 * 5) - scaleSvg(pitchElement.width / 5 / 2);
                })
                .attr("y", function () {
                    return scaleSvg(1.75);
                })  // Regola la posizione del testo verticalmente
                .attr("text-anchor", "middle")  // Centra il testo rispetto al cerchio
                .attr("fill", "white")  // Colore del testo
                .attr("font-size", calculateTextSize(20))
                .text(function () {
                    return "C5";
                });
        }

        // corners
        function addPath(pathData, parentElement, size) {
            var path = parentElement.append("path")
                .attr("d", pathData)
                .attr("stroke", pitchElement.paintColor)
                .attr("fill", "none");

            if (typeof size !== 'undefined') {
                path.attr("stroke-width", size);
            }
        }

        // top left
        var pathData = "M0," + scaleSvg(1) + "A " + scaleSvg(1) + " " + scaleSvg(1) + " 45 0 0" + scaleSvg(1) + ",0";
        addPath(pathData, baseLayer);

        // top right
        pathData = "M" + scaleSvg(pitchElement.width - 1) + ",0 A " + scaleSvg(1) + " " + scaleSvg(1) + " 45 0 0" + scaleSvg(pitchElement.width) + "," + scaleSvg(1);
        addPath(pathData, baseLayer);

        // bottom left
        pathData = "M0," + scaleSvg(pitchElement.length - 1) + "A " + scaleSvg(1) + " " + scaleSvg(1) + " 45 0 1" + scaleSvg(1) + "," + scaleSvg(pitchElement.length);
        addPath(pathData, baseLayer);

        // top right
        pathData = "M" + scaleSvg(pitchElement.width - 1) + "," + scaleSvg(pitchElement.length) + " A " + scaleSvg(1) + " " + scaleSvg(1) + " 45 0 1" + scaleSvg(pitchElement.width) + "," + scaleSvg(pitchElement.length - 1);
        addPath(pathData, baseLayer);

        // Top Penalty Area
        var penaltyAreaTop = baseLayer.append("g");
        pathData = "M" + tmpScale(14) + ",0L" + tmpScale(14) + "," + tmpScale(16.5) + "L" + tmpScale(54) + "," + tmpScale(16.5) + "L" + tmpScale(54) + ",0";
        addPath(pathData, penaltyAreaTop);

        // Top Penalty Area
        pathData = "M" + tmpScale(24.84) + ",0L" + tmpScale(24.84) + "," + tmpScale(5.5) + "L" + tmpScale(43.16) + "," + tmpScale(5.5) + "L" + tmpScale(43.16) + ",0";
        addPath(pathData, penaltyAreaTop);

        // Top D
        pathData = "M" + tmpScale(42) + "," + tmpScale(16.5) + "A " + baseBezier + " " + baseBezier + " 5 0 1 " + tmpScale(26) + "," + tmpScale(16.5);
        addPath(pathData, penaltyAreaTop);

        // Top Penalty Spot
        penaltyAreaTop.append("circle")
            .attr("cx", tmpScale(34))
            .attr("cy", tmpScale(11))
            .attr("r", 1)
            .attr("fill", pitchElement.paintColor)
            .attr("stroke", pitchElement.paintColor);

        var penaltyAreaBottom = baseLayer.append("g");
        penaltyAreaBottom.html(penaltyAreaTop.html());
        penaltyAreaBottom.attr("transform", "rotate(180) translate(-" + scaleSvg(pitchElement.width) + ",-" + scaleSvg(pitchElement.length) + ")");

        // Cache a clone of the baseLayer node
        baseLayerCache.set(cacheKey, baseLayer.node().cloneNode(true));
    }

    pitchElement.baseLayer = baseLayer;

    var pointLayer = svg.append("g")
        .attr("data-index", "1")
        .attr("transform", "translate(" + scaleSvg(pitchElement.padding.left) + "," + scaleSvg(pitchElement.padding.top) + ")");

    pitchElement.pointLayer = pointLayer;
}

function drawEvent(event, normalized, pitchElement, settings) {
    var startPos, endPos, isTouchPoint = false;

    if (typeof pitchElement === "undefined") {
        pitchElement = pitch;
    }

    if (typeof normalized === 'undefined' || normalized) {
        startPos = event.startPointNormalized;
        endPos = event.endPointNormalized;
    } else {
        startPos = event.startPoint;
        endPos = event.endPoint;
    }

    var isVertical = pitchElement.isVertical;
    if (isVertical) {
        startPos = getVerticalCoord(startPos);
        endPos = getVerticalCoord(endPos);
    }

    var scaledRaggio = raggio * multiplier;

    var mType = '';
    if (eventTypes.has(event.eventTypeId)) {
        mType = eventTypes.get(event.eventTypeId).code;
    }

    if (settings && settings.isTouchPoints) {
        isTouchPoint = true;
    }

    if (startPos && !startPos.isDefault) {
        var firstPoint = {
            cx: scale(startPos.x),
            cy: scale(startPos.y),
            r: scaledRaggio
        };
        var originalFirstPoint = {...firstPoint};
        var secondPoint, originalSecondPoint;

        var color = getEventColor(event);
        if (settings && settings.color) {
            color = settings.color;
        }
        var skipShape = false;
        if (settings && settings.skipShape || isTouchPoint) {
            skipShape = true;
        }

        if ((mType === 'DLL' || mType === 'DLS' ||
                mType === 'DRF' || mType === 'DRS' ||
                mType === 'INT') && !skipShape) {
            pitchElement.pointLayer.append("rect")
                    .attr("x", firstPoint.cx - scaledRaggio - (scaledRaggio / 2))
                    .attr("y", firstPoint.cy - scaledRaggio - (scaledRaggio / 2))
                    .attr("width", scaledRaggio * 2)
                    .attr("height", scaledRaggio * 2)
                    .attr("fill", color)
                    .attr("data-event", "event-" + event.id);

            firstPoint.cx = firstPoint.cx - scaledRaggio / 2;
            firstPoint.cy = firstPoint.cy - scaledRaggio / 4;
        } else if ((mType === 'FAF' || mType === 'AMM' || mType === 'ESP') && !skipShape) {
            var firstCoord = (firstPoint.cx - scaledRaggio * 2 - scaledRaggio / 2) + "," + (firstPoint.cy + scaledRaggio);
            var secondCoord = firstPoint.cx - scaledRaggio / 2 + "," + (firstPoint.cy - scaledRaggio * 2);
            var thirdCoord = (firstPoint.cx + scaledRaggio * 2 - scaledRaggio / 2) + "," + (firstPoint.cy + scaledRaggio);

            pitchElement.pointLayer.append("polygon")
                    .attr("points", firstCoord + " " + secondCoord + " " + thirdCoord)
                    .attr("fill", color)
                    .attr("data-event", "event-" + event.id);

            firstPoint.cx = firstPoint.cx - scaledRaggio / 2;
            firstPoint.cy = firstPoint.cy + scaledRaggio / 4;
        } else {
            // da lasciare altrimenti per gli eventi che si connettono mostra entrambi i pallini
            if (isTouchPoint) {
                pitchElement.pointLayer.append("circle")
                        .attr("cx", firstPoint.cx - scaledRaggio / 2)
                        .attr("cy", firstPoint.cy - scaledRaggio / 2)
                        .attr("r", scaledRaggio)
                        .attr("fill", color)
                        /*.attr("stroke", "#000000")
                        .attr("stroke-width", 2)*/
                        .attr("data-event", "event-" + event.id);
            }

            originalFirstPoint.cx = firstPoint.cx - scaledRaggio / 2;
            originalFirstPoint.cy = firstPoint.cy - scaledRaggio / 2;
            firstPoint.cx = firstPoint.cx - scaledRaggio / 2;
            firstPoint.cy = firstPoint.cy - scaledRaggio / 4;
        }

        if (endPos && !endPos.isDefault && !isTouchPoint) {
            secondPoint = {
                cx: scale(endPos.x),
                cy: scale(endPos.y),
                r: scaledRaggio
            };
            originalSecondPoint = {...secondPoint};

            originalSecondPoint.cx = secondPoint.cx - scaledRaggio / 2;
            originalSecondPoint.cy = secondPoint.cy - scaledRaggio / 2;

            if (firstPoint && secondPoint) {
                var drawArrow = !(scale(event.distance) <= scale(4));
                connectPoints(pitchElement, originalFirstPoint, originalSecondPoint, drawArrow, event, false, settings);
            }

            pitchElement.pointLayer.append("circle")
                    .attr("cx", secondPoint.cx - scaledRaggio / 2)
                    .attr("cy", secondPoint.cy - scaledRaggio / 2)
                    .attr("r", scaledRaggio)
                    .attr("fill", color)
                    .attr("stroke", "#000000")
                    .attr("stroke-width", 1)
                    .attr("data-event", "event-" + event.id);

            secondPoint.cx = secondPoint.cx - scaledRaggio / 2;
            secondPoint.cy = secondPoint.cy + scaledRaggio / 4;
        }
    } else {
        if (!(mType === 'SUB' || mType === 'SOS')) {
            console.warn("drawEvent()", "can't draw event id " + event.id + ". Coords are empty");
        }
    }
}

function connectPoints(pitch, firstPoint, secondPoint, drawArrow, event, isConduzione, settings) {
    // console.log(pointLayer, firstPoint, secondPoint, drawArrow);
    var distance = getPointsDistance(firstPoint, secondPoint);

    if (distance > minDistance) {
        // colore freccia
        var lineColor = getEventColor(event);
        if (typeof isConduzione !== 'undefined' && isConduzione) {
            // conduzione sempre nera
            lineColor = lineColors.black;
        }

        // controllo se c'è bisogno di creare la freccia
        if (typeof drawArrow !== 'undefined' && drawArrow) {
            var isArrowheadPresent = !pitch.pointLayer.select("#arrowhead" + lineColor.replace("#", "")).empty();
            if (!isArrowheadPresent) {
                pitch.pointLayer.append("defs").append("marker")
                        .attr("id", "arrowhead" + lineColor.replace("#", ""))
                        .attr("refX", 2)
                        .attr("refY", 2)
                        .attr("markerWidth", 6)
                        .attr("markerHeight", 4)
                        .attr("orient", "auto")
                        .append("path")
                        .attr("d", "M0,0 L6,2 L0,4")
                        .style("fill", lineColor);
            }

            isArrowheadPresent = !pitch.pointLayer.select("#arrowheadHighlighted").empty();
            if (!isArrowheadPresent) {
                pitch.pointLayer.append("defs").append("marker")
                        .attr("id", "arrowheadHighlighted")
                        .attr("refX", 2)
                        .attr("refY", 2)
                        .attr("markerWidth", 6)
                        .attr("markerHeight", 4)
                        .attr("orient", "auto")
                        .append("path")
                        .attr("d", "M0,0 L6,2 L0,4")
                        .style("fill", "lime");
            }
        }

        // dati primo punto
        var x1 = parseFloat(firstPoint.cx);
        var y1 = parseFloat(firstPoint.cy);
        var r1 = parseFloat(firstPoint.r);
        // dati secondo punto
        var x2 = parseFloat(secondPoint.cx);
        var y2 = parseFloat(secondPoint.cy);
        var r2 = parseFloat(secondPoint.r);

        var angle = Math.atan2(y2 - y1, x2 - x1);
        var startX = x1 + r1 * Math.cos(angle);
        var startY = y1 + r1 * Math.sin(angle);
        var endX = x2 - (r2) * Math.cos(angle);
        var endY = y2 - (r2) * Math.sin(angle);

        var points = findPerpendicularPoints(parseFloat(firstPoint.cx), parseFloat(firstPoint.cy), parseFloat(secondPoint.cx), parseFloat(secondPoint.cy), 3);

        var color = "#000000";
        if (settings && settings.arrowColor) {
            color = getEventColor(event);
        }

        var trianglePath = "M" + startX + "," + startY +
                " L" + points[0].x + "," + points[0].y +
                " L" + points[1].x + "," + points[1].y +
                " L" + startX + "," + startY;
        var path = pitch.pointLayer.append("path")
                .attr("d", trianglePath)
                .attr("stroke", color)
                .attr("stroke-width", 1)
                .attr("fill", color + "99");
        path.lower();

        if (settings) {
            if (settings.showPlayerNumber) {
                // Add player number at the start of the arrow
                if (event && event.playerIds) {
                    var playerId = event.playerIds[0];
                    if (fixturePlayers.has(playerId)) {
                        var player = fixturePlayers.get(playerId);
                        if (player.jerseyNumber) {
                            var textX = startX; // Position slightly to the right of the arrow start
                            var textY = startY - 5; // Position slightly below the arrow start

                            pitch.pointLayer.append("text")
                                .attr("x", textX)
                                .attr("y", textY)
                                .attr("text-anchor", "middle")
                                .attr("alignment-baseline", "middle")
                                .attr("font-size", 8)
                                .attr("fill", "#FFFFFF")
                                .attr("stroke", "#000000")
                                .attr("stroke-width", 0.5)
                                .attr("font-weight", "bold")
                                .text(player.jerseyNumber);
                        }
                    }
                }
            }
            if (settings.showPlayerToNumber) {
                // Add playerTo number at the end of the arrow
                if (event && event.playerToIds) {
                    var playerToId = event.playerToIds[0];
                    if (fixturePlayers.has(playerToId)) {
                        var playerTo = fixturePlayers.get(playerToId);
                        if (playerTo.jerseyNumber) {
                            var textX = endX; // Position slightly to the right of the arrow end
                            var textY = endY - 7; // Position slightly below the arrow end

                            pitch.pointLayer.append("text")
                                .attr("x", textX)
                                .attr("y", textY)
                                .attr("text-anchor", "middle")
                                .attr("alignment-baseline", "middle")
                                .attr("font-size", 8)
                                .attr("fill", "#FFFFFF")
                                .attr("stroke", "#000000")
                                .attr("stroke-width", 0.5)
                                .attr("font-weight", "bold")
                                .text(playerTo.jerseyNumber);
                        }
                    }
                }
            }
        }
    }
}

function findPerpendicularPoints(startX, startY, endX, endY, distance) {
    var newM = 0;
    var m;
    if (startX !== endX && startY !== endY) {
        m = (endY - startY) / (endX - startX);
        newM = -1 / m;
    }

    var magnitude = Math.sqrt(1 + (newM * newM));

    var deltaX = distance / magnitude;
    var deltaY = (distance * newM) / magnitude;

    var x1 = endX + deltaX;
    var y1 = endY + deltaY;

    var x2 = endX - deltaX;
    var y2 = endY - deltaY;

    // Calcola i nuovi punti a distanza specificata
    const point1 = {
        x: x1,
        y: y1
    };

    const point2 = {
        x: x2,
        y: y2
    };

    return [point1, point2];
}

/*
 * UTILS
 */
function calculateTextNumberSize(number) {
    var baseSize = 10;

    if (number > 9) {
        baseSize = 8;
    }

    return baseSize * multiplier;
}

function calculateTextSize(number) {
    return number * multiplier;
}

function getEventColor(event) {
    var lineColor = lineColors.black;
    if (typeof event !== 'undefined' && event) {
        // Map eventTypeId to mType string
        function mapEventTypeIdToType(eventTypeId) {
            const typeMap = {
                1: 'DLL', 2: 'DLS', 3: 'DRF', 4: 'DRS', 5: 'INT', 6: 'FAF', 7: 'AMM', 8: 'ESP', 9: 'SUB', 10: 'SOS', 11: 'TIF', 12: 'PASS'
            };
            return typeMap[eventTypeId] || '';
        }
        var mType = mapEventTypeIdToType(event.eventTypeId);
        if (mType === 'TIF') { // tiri rossi
            lineColor = lineColors.red;
        } else if (mType === 'PASS') {
            // If tagTypeIds contains a code for PASS-0, set blue
            if (Array.isArray(event.tagTypeIds)) {
                // You may need a mapping from tagTypeId to code, here we just check for a specific ID (example: 100 means PASS-0)
                if (event.tagTypeIds.includes(100)) {
                    lineColor = lineColors.blue;
                }
            }
        }
    }

    return lineColor;
}

function getPointsDistance(firstPoint, secondPoint) {
    if (firstPoint && !firstPoint.isDefault && secondPoint && !secondPoint.isDefault) {
        var x = firstPoint.cx - secondPoint.cx;
        var y = firstPoint.cy - secondPoint.cy;

        return Math.sqrt(x * x + y * y);
    } else {
        return 0;
    }
}

function getVerticalCoord(pos) {
    var newPos = {...pos};
    newPos.x = 105 - newPos.x;
    var tmp = newPos.x;
    newPos.x = newPos.y;
    newPos.y = tmp;

    return newPos;
}

function getEventColor(event) {
    if (event.eventTypeId === 9) {
        if (event.tagTypeIds && event.tagTypeIds.includes(68)) {
            return "#0000ff"; // DRIBBLING VINTI
        } else {
            return "#ffff00"; // DUELLI VINTI
        }
    } else if (event.eventTypeId === 23) {
        if (event.tagTypeIds && event.tagTypeIds.includes(127)) {
            return "#00ffff"; // DRIBBLING PERSO
        } else {
            return "#ffc800"; // DUELLO PERSO
        }
    } else if (event.eventTypeId === 34) {
        if (event.tagTypeIds && event.tagTypeIds.includes(416)) {
            if (event.distance && event.distance > 40) {
                return "#0000ff"; // LANCIO FATTO
            } else {
                return "#ffc800"; // PASSAGGIO FATTO
            }
        }
    } else if (event.eventTypeId === 14) {
        if (event.tagTypeIds && event.tagTypeIds.includes(88)) {
            return "#ffc800"; // RINVIO MANO
        } else if (event.tagTypeIds && event.tagTypeIds.includes(89)) {
            return "#0000ff"; // RINVIO DAL FONDO
        } else if (event.tagTypeIds && event.tagTypeIds.includes(87)) {
            return "#00ffff"; // RINVIO PIEDE
        }
    }

    return "#FFFFFF";
}