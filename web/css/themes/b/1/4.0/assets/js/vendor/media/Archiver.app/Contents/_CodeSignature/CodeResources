<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Resources/7za</key>
		<data>
		VHUATIv/hWgXDETGrmbzPeeaa9w=
		</data>
		<key>Resources/7zip.icns</key>
		<data>
		7xR+gHAmAyiUexdWMRlzOC55+gc=
		</data>
		<key>Resources/Acknowledgements.txt</key>
		<data>
		7jRqHCzYpCtdly5VTdrF0lKWNak=
		</data>
		<key>Resources/Assets.car</key>
		<data>
		Xzx6oKjdbOJ5Y+3bGK0o0K3KjvA=
		</data>
		<key>Resources/Credits.html</key>
		<data>
		/8WH3L/jFx53eufEpl2BBO37I+E=
		</data>
		<key>Resources/License.html</key>
		<data>
		pDjcRHJPZBnxfqAoXFupdmtO1es=
		</data>
		<key>Resources/Updates.tiff</key>
		<data>
		nd8gjMfJ7rvPWAgakaWSLJvahi0=
		</data>
		<key>Resources/adf.icns</key>
		<data>
		zBvydFm7MnyYGakEGhN8cMMJ9wo=
		</data>
		<key>Resources/arch.icns</key>
		<data>
		gp1Yg2dZAWUqS5cKlU7uSn9qZbA=
		</data>
		<key>Resources/archiver.icns</key>
		<data>
		J17fPf/atlPvwcefierTm2NID/k=
		</data>
		<key>Resources/arj.icns</key>
		<data>
		aAiFqCFUjqY+HHcnnRoPuNdx40Q=
		</data>
		<key>Resources/binary.icns</key>
		<data>
		HnSlfa4VLZ6taOcTExKtFIvHpGQ=
		</data>
		<key>Resources/cab.icns</key>
		<data>
		w83oAuAgpiDP0yGDx3gkEUQ9WVE=
		</data>
		<key>Resources/chksplit.icns</key>
		<data>
		qw2K0K10ap9R8NZSlGDSDPjNt+o=
		</data>
		<key>Resources/cpio.icns</key>
		<data>
		xs3O4j77cJETSvr1mP083zcCEtI=
		</data>
		<key>Resources/de.lproj/BEApplicationHelper.strings</key>
		<dict>
			<key>hash</key>
			<data>
			qBwKUXEJER+vNDLSNjkmvwMWbec=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/BEBytesValueTransformer.strings</key>
		<dict>
			<key>hash</key>
			<data>
			exmtq/9x5ZAfk8/RFIruLv/QuR8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/BEPreferencesWindowController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			KTnvmqlJjDsVIdnYmC4cthR6cH0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/BESandboxFileAuthenticator.strings</key>
		<dict>
			<key>hash</key>
			<data>
			YBEYuhUYhf5xE+FvqYJJO+7tLY0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/DropBox.strings</key>
		<dict>
			<key>hash</key>
			<data>
			jolV7QgLPw99npr7KdWNJb2rLxI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/EditArchive.strings</key>
		<dict>
			<key>hash</key>
			<data>
			RnVzY399HkBMC9i5+JL6VX8XDjU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/LicenseAgreement.strings</key>
		<dict>
			<key>hash</key>
			<data>
			RWhHAExUgvq1hZELn3sUhRfFLZM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			q8GLbC+Me3IiBBrgNBXy6EPL82k=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/MainMenu.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ufz9bWNDafOtlTrVtVAIxL+WXC8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/MoveApplication.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GqP2NupuAqmyJYwzEvAIDPzf5yM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/MultipleFileOperationCompleted.strings</key>
		<dict>
			<key>hash</key>
			<data>
			o1aTt2Rn93QHbbw0rakwqf4BqaM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/OperationCompleted.strings</key>
		<dict>
			<key>hash</key>
			<data>
			X2J2VQ3KMI5U/GGaRDaiILfKTbA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/Password.strings</key>
		<dict>
			<key>hash</key>
			<data>
			nRR2Xs8+Kj1zctibxTz5V528eKM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/Preferences.strings</key>
		<dict>
			<key>hash</key>
			<data>
			5PqzLRoKRWG47WvqBVkKMfC64eI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/Progress.strings</key>
		<dict>
			<key>hash</key>
			<data>
			khJfar76h7x7bFnKVz4EZ24n5+A=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/Registration.strings</key>
		<dict>
			<key>hash</key>
			<data>
			DZkOm9cSC+hoCmmtKL+Fq4TwS2s=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/SplitFile.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GiVsagx9RJT1DroH9kxAI3AhJwk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/Window.strings</key>
		<dict>
			<key>hash</key>
			<data>
			5fm9yt2ESKj0KY19HTZrCXwzueM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/dsa_pub.pem</key>
		<data>
		iDejVrHdYfy6jgvn2oH4vV3+WoQ=
		</data>
		<key>Resources/en.lproj/Alert.nib</key>
		<dict>
			<key>hash</key>
			<data>
			77LizROPT2KoYoQYiToTNTKmJyE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Alert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GCi03DyQurxzZ8wbOsH/xinx5gI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/Archiver.Help.helpindex</key>
		<dict>
			<key>hash</key>
			<data>
			e2ThX9SZ5Z29cSRaHokh/inbfE0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/ArchiverHelp.html</key>
		<dict>
			<key>hash</key>
			<data>
			myudWW0bUsllg3b+ur4k1vYElGU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/access_border2_bottom.gif</key>
		<dict>
			<key>hash</key>
			<data>
			MTHgW7gIx/qXjg1FnmDDyn//yXc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/access_border2_bottomright.gif</key>
		<dict>
			<key>hash</key>
			<data>
			AWiaWSrqquNV3xqY9iILXllRzSg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/access_border2_innerbottomleft.gif</key>
		<dict>
			<key>hash</key>
			<data>
			OUYf0ySq46eRm456/IsGPLavViw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/access_border2_innerleft.gif</key>
		<dict>
			<key>hash</key>
			<data>
			sYAXRQhzVVmYwGikB5xyHS3aA/M=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/access_border2_innertopleft.gif</key>
		<dict>
			<key>hash</key>
			<data>
			kyG+Ljnpw3+SxXDq+xrxDfb08OY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/access_border2_right.gif</key>
		<dict>
			<key>hash</key>
			<data>
			gNCunYK4lgmh0jL1qlOAF0KugdU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/access_border2_top.gif</key>
		<dict>
			<key>hash</key>
			<data>
			1VMv6NZ3eeoiaSm9ZXG0IK3cEqs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/access_border2_topright.gif</key>
		<dict>
			<key>hash</key>
			<data>
			JN+mFb/pYIz0RsdWxX17EdYKkcM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/access_border_bottom.gif</key>
		<dict>
			<key>hash</key>
			<data>
			Qm7wn+mWTvhVr1Ke8z7xa70UmDE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/access_border_bottomleft.gif</key>
		<dict>
			<key>hash</key>
			<data>
			aQCm/sRoy9hDW7uT4YqvWj7Gdlo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/access_border_bottomright.gif</key>
		<dict>
			<key>hash</key>
			<data>
			u7n02Q/CaE0IvZUso6kpv0HFaW8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/access_border_innerbottomleft.gif</key>
		<dict>
			<key>hash</key>
			<data>
			uPKT/Z3FQzMhtrLpTM1194v4or4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/access_border_innerbottomright.gif</key>
		<dict>
			<key>hash</key>
			<data>
			Wgj5ibyUimOCl7JDpLyz3D59Xbc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/access_border_innerleft.gif</key>
		<dict>
			<key>hash</key>
			<data>
			nuf7bNDFsdFi2snAWRJdN6z1riI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/access_border_innerright.gif</key>
		<dict>
			<key>hash</key>
			<data>
			K4ghkkYmZ9F9yYs1bWm+2IuY9mY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/access_border_innertopleft.gif</key>
		<dict>
			<key>hash</key>
			<data>
			O0jx6iPev1BuV1U9kUtI0UAl//c=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/access_border_innertopright.gif</key>
		<dict>
			<key>hash</key>
			<data>
			nk90kwEma2QP3MvxePlHcwbGUx4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/access_border_left.gif</key>
		<dict>
			<key>hash</key>
			<data>
			llc9lGqv4orBULFQ87yBGo/ck2M=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/access_border_right.gif</key>
		<dict>
			<key>hash</key>
			<data>
			wq7julbN0b3Tu2J3axbkMlEzrXg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/access_border_top.gif</key>
		<dict>
			<key>hash</key>
			<data>
			NGyZpz4tzXaeYbS83Q80BoPWsts=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/access_border_topleft.gif</key>
		<dict>
			<key>hash</key>
			<data>
			VGOWqiccGG2IXQfCxxsRRoO7k3E=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/access_border_topright.gif</key>
		<dict>
			<key>hash</key>
			<data>
			BmhLZixe0UYlDTOJ8Q8ndCI4/5E=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/arrow.gif</key>
		<dict>
			<key>hash</key>
			<data>
			p1wsjAbqdXGWelNpmNS9iE1i5l0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/ax_term.jpg</key>
		<dict>
			<key>hash</key>
			<data>
			YhskXpHy4Pesw1iowvD2PpokZTE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/border_bottom.gif</key>
		<dict>
			<key>hash</key>
			<data>
			gxexh5oNlyIcvx7kCJsIfrW9iUs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/border_bottomleft.gif</key>
		<dict>
			<key>hash</key>
			<data>
			rbtMjA+ZvrAlOJL+qyHkTF+vJmA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/border_bottomright.gif</key>
		<dict>
			<key>hash</key>
			<data>
			W07wEVoQrShG8BNt9SdShYGnfnc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/border_left.gif</key>
		<dict>
			<key>hash</key>
			<data>
			P2hahboA/X3hhr0AZFpnpP3do70=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/border_right.gif</key>
		<dict>
			<key>hash</key>
			<data>
			dg3p4BQong7+o+n/VSwFo5eBDQQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/border_top.gif</key>
		<dict>
			<key>hash</key>
			<data>
			Akll7MiBZc/dvtduTkjpUon1RZg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/border_topleft.gif</key>
		<dict>
			<key>hash</key>
			<data>
			EJxvwPb2nTfIG2eBjb6Cn/c591U=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/border_topright.gif</key>
		<dict>
			<key>hash</key>
			<data>
			xFdymNEBwVvYIe1njffIWOu6Kf4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/bullet.gif</key>
		<dict>
			<key>hash</key>
			<data>
			79q65zQta5MA2s/y6u8oWkP0Q1Y=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/grid.tiff</key>
		<dict>
			<key>hash</key>
			<data>
			nw1L22KOk3sig9m7h3OvDOv05eY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/hlp24a.jpg</key>
		<dict>
			<key>hash</key>
			<data>
			4PlzFtkSDZ7Afl5KU++55LyoD24=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/hlp24b.jpg</key>
		<dict>
			<key>hash</key>
			<data>
			q9jnZbXRmM+24uINuN8KMbUs+r4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/icnfdbck.gif</key>
		<dict>
			<key>hash</key>
			<data>
			53gL3ORUHR+66DDzPcZq1kMxt44=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/icon.png</key>
		<dict>
			<key>hash</key>
			<data>
			LvOUE1qqEI1o9iRFFcUU8zwGH50=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/iconSmall.png</key>
		<dict>
			<key>hash</key>
			<data>
			mpgIyvhJXBVXwgeIWYbsvhxS+/c=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/index_circle.gif</key>
		<dict>
			<key>hash</key>
			<data>
			mzXokb1lK2YyG9PJou2brOlXtbc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/index_circle_lefthalf.gif</key>
		<dict>
			<key>hash</key>
			<data>
			bP2G6e4rxMIlUIL+ktPQzXBPZiI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/index_circle_righthalf.gif</key>
		<dict>
			<key>hash</key>
			<data>
			RZTE+wgAnCLAp+3bUEi7fGmGgD8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/ipodbk.gif</key>
		<dict>
			<key>hash</key>
			<data>
			EHC67NSfc9aiFVT2ZbgJ1YOO6r0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/list.tiff</key>
		<dict>
			<key>hash</key>
			<data>
			AXb1XRbMIh0BSuqDuTbnXo+erJ0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/orngbullet.gif</key>
		<dict>
			<key>hash</key>
			<data>
			vLehZjvpyNzuIQryQi9ZOjSpgaE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/rule.gif</key>
		<dict>
			<key>hash</key>
			<data>
			ez4kz3WrIYYtNN16Uv29t9cjfAI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/step_1.gif</key>
		<dict>
			<key>hash</key>
			<data>
			a5vtIeUEHcd5eE20CMBx8wL/XuE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/step_10.gif</key>
		<dict>
			<key>hash</key>
			<data>
			YU405Re7RXSldrhipKGD6KvMLUk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/step_2.gif</key>
		<dict>
			<key>hash</key>
			<data>
			Aw+w9yUxZ6Twbh+ykdyIBIgfPCg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/step_3.gif</key>
		<dict>
			<key>hash</key>
			<data>
			x3NZQL6ldiDDmqKTtj0IYGVjttY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/step_4.gif</key>
		<dict>
			<key>hash</key>
			<data>
			Ob764qmyN874mTdVS0ay0chafdI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/step_5.gif</key>
		<dict>
			<key>hash</key>
			<data>
			vYD9QJen3nDs/2lQGKIkpapSONQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/step_6.gif</key>
		<dict>
			<key>hash</key>
			<data>
			aTcnUlrqC2hPWzbRvQwQ7ao/U6Q=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/step_7.gif</key>
		<dict>
			<key>hash</key>
			<data>
			IXbQ5z2JMusIaBih5yr1zA5K4vw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/step_8.gif</key>
		<dict>
			<key>hash</key>
			<data>
			O72G2JJfc9FgOjGRsCH0BahdI3I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/step_9.gif</key>
		<dict>
			<key>hash</key>
			<data>
			/fER6WkbzZIBmqPQZPht74PVzqs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/table_mid_3.gif</key>
		<dict>
			<key>hash</key>
			<data>
			17Kyfq5RqyqZlgNyitTpYbU8iZQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/table_mid_5.gif</key>
		<dict>
			<key>hash</key>
			<data>
			KiKVckkbQk58gjrUNIreD0fp2ik=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/tableend_left_3px.gif</key>
		<dict>
			<key>hash</key>
			<data>
			PRd3wBgUaHNabT34yU/olOVT4fw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/tableend_left_5px.gif</key>
		<dict>
			<key>hash</key>
			<data>
			Nv/S/4K7zb+Rxxs1bpvYgIEvsBo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/tableend_right_3px.gif</key>
		<dict>
			<key>hash</key>
			<data>
			rX6kk97+RYS9BaeHEsSscS/aKB4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/tableend_right_5px.gif</key>
		<dict>
			<key>hash</key>
			<data>
			XPRINv9wah5gAWWC0Oqmfpx4Lgc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/trm2.jpg</key>
		<dict>
			<key>hash</key>
			<data>
			kGiKGQGRStTIBCqZz4hJbkbyXHQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/pgs/about.html</key>
		<dict>
			<key>hash</key>
			<data>
			cxGfJ2f708jVkEQV03J3V/h6vac=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/pgs/p5421.html</key>
		<dict>
			<key>hash</key>
			<data>
			IbYVtezR9ARMxNTmY4Mj1EBCz2c=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/pgs/p5422.html</key>
		<dict>
			<key>hash</key>
			<data>
			6noEZLWh1EjDPluVCDl5x9/6fbg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/pgs/p5423.html</key>
		<dict>
			<key>hash</key>
			<data>
			uCmxpz0+qlu04DtiRFl71pfyikc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/pgs/p5424.html</key>
		<dict>
			<key>hash</key>
			<data>
			ZKydncXtw5hrKFR2+6DnDlidy38=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/pgs/p5425.html</key>
		<dict>
			<key>hash</key>
			<data>
			oHzPpkbD1OFERsDdqrr/CoEwEcw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/pgs/p5426.html</key>
		<dict>
			<key>hash</key>
			<data>
			4TBrOO3NigHgZCsSoX0LXomFrBU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/pgs/p5427.html</key>
		<dict>
			<key>hash</key>
			<data>
			K3Xg3Twr2ijJajv3WYjgj6/Hcok=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/pgs/p5428.html</key>
		<dict>
			<key>hash</key>
			<data>
			DW0Pa+gVs7USuzG7DDZ7kbalYRg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/pgs/p5429.html</key>
		<dict>
			<key>hash</key>
			<data>
			bmez/mLBOOm5sFA0MrVmp0TLsSs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/pgs/p5430.html</key>
		<dict>
			<key>hash</key>
			<data>
			1MKJwHeVWtBPRKcec7iEABfhbJA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/pgs/p5431.html</key>
		<dict>
			<key>hash</key>
			<data>
			N2QiKWrahfjUfFEWq03SAYr3QUI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/pgs/p5432.html</key>
		<dict>
			<key>hash</key>
			<data>
			KRKKy7XMdC/isYkM9GPz6lxYIHI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/pgs/p5433.html</key>
		<dict>
			<key>hash</key>
			<data>
			wAjeyIVIJmfMjMOArd3b+PdyX2I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/pgs/p5434.html</key>
		<dict>
			<key>hash</key>
			<data>
			oBdWiJWVZXKy/ETXGp6+mXxLULw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/pgs/p5435.html</key>
		<dict>
			<key>hash</key>
			<data>
			s/f4GEFjCpwzvfrk1e5YhBD9mTc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/pgs/p5436.html</key>
		<dict>
			<key>hash</key>
			<data>
			LKf3meEB2+LSIOLlx5IkUOqQjIg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/pgs/p5437.html</key>
		<dict>
			<key>hash</key>
			<data>
			l137sDlQUuYomkRbLAkTbi1jdpA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/pgs/p5438.html</key>
		<dict>
			<key>hash</key>
			<data>
			tfrWHdC1Tvo54g1TUMbAbCGl2UM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/pgs/p5439.html</key>
		<dict>
			<key>hash</key>
			<data>
			2I4V5d3Qj/HmtNTdUAizSXEtXns=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/pgs/p5440.html</key>
		<dict>
			<key>hash</key>
			<data>
			qKxRlRGTrlJt4A8wNp0JdwNxMtg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/pgs/p5441.html</key>
		<dict>
			<key>hash</key>
			<data>
			lyWzykymyfDtiBN/AaMdhYTS3sQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/pgs/p5442.html</key>
		<dict>
			<key>hash</key>
			<data>
			rKSeNEGsuGKKy8vlPGQBsjoeb6I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/pgs/p5443.html</key>
		<dict>
			<key>hash</key>
			<data>
			xLwZ/TOn1hrvh7/G2DvyBaA9B9A=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/pgs/p5444.html</key>
		<dict>
			<key>hash</key>
			<data>
			1834OWsjJyAtzfydVuN37h/0muc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/pgs/solvingProblems.html</key>
		<dict>
			<key>hash</key>
			<data>
			OsIMPxD5Hc3PU2tSQ1fSRr3df9g=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/pgs/whatsNew.html</key>
		<dict>
			<key>hash</key>
			<data>
			n+rTQ7ZweWg19xbIpCFDzdPrejI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/shrd/OpnApp.scpt</key>
		<dict>
			<key>hash</key>
			<data>
			J4yBhZ8pcBApPSN5ZTwxAxGdwvk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/shrd/OpnAppBndID.scpt</key>
		<dict>
			<key>hash</key>
			<data>
			E5B54fafJfDzid3/mrikHEFhYgs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/shrd/OpnAppDict.scpt</key>
		<dict>
			<key>hash</key>
			<data>
			ZUQIe4ZPlNh3cZbtBgWKG7jjFHw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/shrd/OpnFile.scpt</key>
		<dict>
			<key>hash</key>
			<data>
			qP2TsAWrSSDCQ9pSmmhMCx+wGWE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/shrd/OpnPrefsBndID.scpt</key>
		<dict>
			<key>hash</key>
			<data>
			PFe0JoHfZilAuMvAzzVx5yEG8zM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/shrd/opnbndsig.scpt</key>
		<dict>
			<key>hash</key>
			<data>
			Nnf7ECglkhzUlp2WX7hnR25mShc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/sty/access_4box.css</key>
		<dict>
			<key>hash</key>
			<data>
			+z9oa5i8gjw9iFTVcPONFBrdQNg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/sty/genlist.html</key>
		<dict>
			<key>hash</key>
			<data>
			NGzVCHAXurEA9XZsWNmw/8JjcGQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/sty/genlist_style.css</key>
		<dict>
			<key>hash</key>
			<data>
			RcFi2LaswRdYRFOIhE0CTF3gcDs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/sty/index.css</key>
		<dict>
			<key>hash</key>
			<data>
			ZY4rGLyIolrH75GJyoyNp7AzQJA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/sty/launchpad.css</key>
		<dict>
			<key>hash</key>
			<data>
			oJEn4CekU2ayrLBzET14SIuRjog=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/sty/print.css</key>
		<dict>
			<key>hash</key>
			<data>
			HE/NniOsTLQMYM9FJ5MNBSO2dlY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/sty/ref.css</key>
		<dict>
			<key>hash</key>
			<data>
			zi8lCIZEWax4hzESw4H2huiZfnQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/sty/task_bulleted.css</key>
		<dict>
			<key>hash</key>
			<data>
			MrqFcwZoTXcQctn11QeKo1+IvkU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/sty/task_numbered.css</key>
		<dict>
			<key>hash</key>
			<data>
			n0gjoaoEk7P+pr5z3Me1EgxgzSk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/sty/task_plain.css</key>
		<dict>
			<key>hash</key>
			<data>
			gPKGYTWWW/XVsCi5mjD1AXZF1CI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/xpgs/xall.html</key>
		<dict>
			<key>hash</key>
			<data>
			zwvx/mBrLJaRo9T3/c7po/4VR+U=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/BEApplicationHelper.strings</key>
		<dict>
			<key>hash</key>
			<data>
			pV9tyd9OCpsNYbLkTakRvnOhpZo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/BEBytesValueTransformer.strings</key>
		<dict>
			<key>hash</key>
			<data>
			FAMTVZf3fzZOA4JU6F17gil/1GU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/BEPreferencesWindowController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			j0z6mimLK1eUS9mUFELleBwScpg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/BESandboxFileAuthenticator.strings</key>
		<dict>
			<key>hash</key>
			<data>
			RtmVRvi2ALifkk2UOZQLjsoYK8w=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/DropBox.nib</key>
		<dict>
			<key>hash</key>
			<data>
			FwyLIo3UrQ3UhxM4aWkKKI1jCc8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/DropBox.strings</key>
		<dict>
			<key>hash</key>
			<data>
			jsY/ZQqVu1eDbZcOfni3V1YtKvM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/EditArchive.nib</key>
		<dict>
			<key>hash</key>
			<data>
			/Qj1qMvqra3GeX393ebVRZ/lxPg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/EditArchive.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ELXM8v7YtXBPSUJZQOwSe5/tzlo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/FileAnalysis.nib</key>
		<dict>
			<key>hash</key>
			<data>
			dwyegAXa1MxuCnfxFcvcorqb6Ds=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/FileAnalysis.strings</key>
		<dict>
			<key>hash</key>
			<data>
			fn28a3eUG7pts2pUxWThF5n9j9s=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/FileOptions.nib</key>
		<dict>
			<key>hash</key>
			<data>
			FsZimmZdnCVo7HlQxyHkhyFIgsQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/FileOptions.strings</key>
		<dict>
			<key>hash</key>
			<data>
			+k1bxoMt94AMtcgphxMFlgYPVvc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/LicenseAgreement.nib</key>
		<dict>
			<key>hash</key>
			<data>
			k/emW0mLqhwQa4ZyEKfL0qHaN74=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/LicenseAgreement.strings</key>
		<dict>
			<key>hash</key>
			<data>
			wGgQ9QGYX6vt/UE3mysyB6Fkf/k=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			gdRvvmRl9e6vZJ4sTwK7iYFFM/I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/MainMenu.nib</key>
		<dict>
			<key>hash</key>
			<data>
			AmhHR8c44uTsClS1FmBrLltQV3s=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/MainMenu.strings</key>
		<dict>
			<key>hash</key>
			<data>
			D3S8q2SRuIrHDVBH4cUUd1IVUIY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/MultipleFileOperationCompleted.nib</key>
		<dict>
			<key>hash</key>
			<data>
			KxVdKY4Bs4lpU4DA6206n7SUdKw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/MultipleFileOperationCompleted.strings</key>
		<dict>
			<key>hash</key>
			<data>
			CeHaaajHdbiLp/QcZYSyq18Wgd0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/OperationCompleted.nib</key>
		<dict>
			<key>hash</key>
			<data>
			OsBdoF62iOhIvDvtOEJh4YxTj78=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/OperationCompleted.strings</key>
		<dict>
			<key>hash</key>
			<data>
			AuP+ZhvYi1aKYZ1l3k2C+4ewM/8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Password.nib</key>
		<dict>
			<key>hash</key>
			<data>
			PlXH3cDf83JHNCWryNYlo7Pk7Yo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Password.strings</key>
		<dict>
			<key>hash</key>
			<data>
			x4YgYwwuMsYV0CFRYownaIM1Qyk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Preferences.nib</key>
		<dict>
			<key>hash</key>
			<data>
			rRovBiDH+JOc3cnkiIVKwD3s0QQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Preferences.strings</key>
		<dict>
			<key>hash</key>
			<data>
			NNYvn4IsE4Q88dtawURkbDQLp/Y=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Preview.nib</key>
		<dict>
			<key>hash</key>
			<data>
			hSYM6AEwtKOPSaE5eQUrBsc4LOY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Preview.strings</key>
		<dict>
			<key>hash</key>
			<data>
			uHO7CMV8xVvCp0lsQDtTCTmKy7w=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Progress.nib</key>
		<dict>
			<key>hash</key>
			<data>
			6VUgyfIuun22HsjBQDups6umMbA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Progress.strings</key>
		<dict>
			<key>hash</key>
			<data>
			m7uyyFi98wbFlDlNR0WOus7ZLbQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/SplitFile.nib</key>
		<dict>
			<key>hash</key>
			<data>
			anp+8FZbDqXO+vij/3/nj7cv04s=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/SplitFile.strings</key>
		<dict>
			<key>hash</key>
			<data>
			F7IZ4ssP8vlp0iCVkACumsQf9Ag=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Window.nib</key>
		<dict>
			<key>hash</key>
			<data>
			A0MoB9IiWTyRoZS9D7lvrfuFO6o=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Window.strings</key>
		<dict>
			<key>hash</key>
			<data>
			BttsSyWN9utAvxi1v2mgduIeChk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/BEApplicationHelper.strings</key>
		<dict>
			<key>hash</key>
			<data>
			djn1kkSbvrqMfo9Om4SSzGhbkOc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/BEBytesValueTransformer.strings</key>
		<dict>
			<key>hash</key>
			<data>
			QchlgEBBeAEzf90ral5FrtskYhE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/BEPreferencesWindowController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			1uArQuQcqyNmd1dVO2cH8JdfmLU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/BESandboxFileAuthenticator.strings</key>
		<dict>
			<key>hash</key>
			<data>
			RAbQY8CACB48ltkqMn1/62Gj8Ec=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/DropBox.strings</key>
		<dict>
			<key>hash</key>
			<data>
			BTNfKTacyNWL7L5duOFQn/Bc4nY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/EditArchive.strings</key>
		<dict>
			<key>hash</key>
			<data>
			XHu3EUIezQqaNrzkYEQVdfDqOtM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/LicenseAgreement.strings</key>
		<dict>
			<key>hash</key>
			<data>
			0zsYrz2OL+gVzlbdMFlOIE2LPmw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			36BQhHXP4/XleUTNHxaEliOBJ9E=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/MainMenu.strings</key>
		<dict>
			<key>hash</key>
			<data>
			FjL6n3NbtVmQE/iWI0z6EjScRRI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/MoveApplication.strings</key>
		<dict>
			<key>hash</key>
			<data>
			fXaxLHybfh7QVz0JSW2EY74Awas=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/MultipleFileOperationCompleted.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Bh4sPdFBqB74WozaajbhOOeoSBs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/OperationCompleted.strings</key>
		<dict>
			<key>hash</key>
			<data>
			pzhEif7vcl8ecnND1c1+1gD1yfU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/Password.strings</key>
		<dict>
			<key>hash</key>
			<data>
			YuoEDDqxJbPDRUIi1YWKiZU2EpA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/Preferences.strings</key>
		<dict>
			<key>hash</key>
			<data>
			L/PYKovaX/ARsndMLMwMaj2ZzRI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/Progress.strings</key>
		<dict>
			<key>hash</key>
			<data>
			+6k5PlgsXQGY7seeDqzeHQ10QBQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/Registration.strings</key>
		<dict>
			<key>hash</key>
			<data>
			hXQB5Z+ZMKgCgyoet5lVapo0kgs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/SplitFile.strings</key>
		<dict>
			<key>hash</key>
			<data>
			upYtvdH86EBqBOYJh58FgRhJvIM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/Window.strings</key>
		<dict>
			<key>hash</key>
			<data>
			nthNnnb9D3CXTG/3r2rzGq/1neo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/filetypes.png</key>
		<data>
		FZDLWMlnYq8HonhfNQazs5xFUQo=
		</data>
		<key>Resources/horizontal_line.tiff</key>
		<data>
		qVObUv8RCKPZ6Mv7RAG/fshnEDI=
		</data>
		<key>Resources/hqx.icns</key>
		<data>
		KzCPx0LFw+P9gUqv43Ss7A20C1I=
		</data>
		<key>Resources/icon.icns</key>
		<data>
		disiNAFYwyBSzoPbxx5CPndnW5s=
		</data>
		<key>Resources/jar.icns</key>
		<data>
		ggvq56PkqGE7GLJ4EmkYTdlHT8w=
		</data>
		<key>Resources/lha.icns</key>
		<data>
		yPvwEEykFE3M6Of481NOJStEEzs=
		</data>
		<key>Resources/pax.icns</key>
		<data>
		jKNGqCIlsK0e3fLVZN6IEogUozM=
		</data>
		<key>Resources/rar.icns</key>
		<data>
		dFmHD9tdQpCULRlxlXmUOfpcw3I=
		</data>
		<key>Resources/rpm.icns</key>
		<data>
		+ku9F6NEF1xmBJBJCfBXICJB0zg=
		</data>
		<key>Resources/ru.lproj/DropBox.strings</key>
		<dict>
			<key>hash</key>
			<data>
			vDMkweL5td0iacy7oJ4T6txrHgE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ru.lproj/EditArchive.strings</key>
		<dict>
			<key>hash</key>
			<data>
			1pPuY/X8lJWA2HN1nB5rVbzaU4E=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ru.lproj/LicenseAgreement.strings</key>
		<dict>
			<key>hash</key>
			<data>
			X/c09hG9MaVs65AWy/oMCA2fjyc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ru.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			uQHhVCSV6Ya2RbEmDYZFcyij/+k=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ru.lproj/MainMenu.strings</key>
		<dict>
			<key>hash</key>
			<data>
			DdxfkIA4FADeNTq6zhdY8Rs9fNM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ru.lproj/MoveApplication.strings</key>
		<dict>
			<key>hash</key>
			<data>
			l/fEBCK/cJTeRNVG4QLlH8PmO9g=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ru.lproj/MultipleFileOperationCompleted.strings</key>
		<dict>
			<key>hash</key>
			<data>
			sOlXtQgG/C44znwOAt6HW3H67lw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ru.lproj/OperationCompleted.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GVESvvQnd4Mmidi2aaQpusw1do8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ru.lproj/Password.strings</key>
		<dict>
			<key>hash</key>
			<data>
			iI2fPY/+kAS4Li1pMTpRZ+0xtcc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ru.lproj/Preferences.strings</key>
		<dict>
			<key>hash</key>
			<data>
			uw0qZuVd9mW7txb08NURAV6f2Ww=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ru.lproj/Progress.strings</key>
		<dict>
			<key>hash</key>
			<data>
			5Ml3rP18Q+Kflt1VbuCxH6NaTcs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ru.lproj/Registration.strings</key>
		<dict>
			<key>hash</key>
			<data>
			SzZLcN6cVNRLvTQMLZTjBlvkFLw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ru.lproj/SplitFile.strings</key>
		<dict>
			<key>hash</key>
			<data>
			hKaghdMaCy8oxKbsCD4flqqXcAs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ru.lproj/Window.strings</key>
		<dict>
			<key>hash</key>
			<data>
			8iNs2Q1YogD9PcE6Bp3+MFdu2EU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sit.icns</key>
		<data>
		hjyLavEGF2QAGTg5URfEBDHIJUE=
		</data>
		<key>Resources/split.icns</key>
		<data>
		Ikj7zlI9PpX1kTPr+LoUTE+U9vM=
		</data>
		<key>Resources/tar-bz.icns</key>
		<data>
		+lrYRRJCLWs1aUBX5ryr/t5x4Z0=
		</data>
		<key>Resources/tar-gz.icns</key>
		<data>
		8zLg4y0nAJnC1tjUAfGMZYw+nT0=
		</data>
		<key>Resources/tar-z.icns</key>
		<data>
		Agt1ks82TNq5/xbM1Pe5jXStPfA=
		</data>
		<key>Resources/tar.icns</key>
		<data>
		MXcFPr3OFrVE6VigRx/CcZpfgWE=
		</data>
		<key>Resources/tbz.icns</key>
		<data>
		nU2C1MXyURFgktlJIEidGpDoZfE=
		</data>
		<key>Resources/tgz.icns</key>
		<data>
		Ocx/rWwQ9XaqYuKqESIPrMRij7M=
		</data>
		<key>Resources/update.png</key>
		<data>
		PaehP0dsXxtLr7WuTV1qUIxbvv4=
		</data>
		<key>Resources/xar.icns</key>
		<data>
		hON9YbwNiLkb+NtSTICX7AXiDtA=
		</data>
		<key>Resources/zip.icns</key>
		<data>
		kJ+HjDRXxQxbEf1xcyZ5UVeNksU=
		</data>
		<key>Resources/zsplit.icns</key>
		<data>
		gzv8BAcr325hYvarqynjSjA3uSs=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Frameworks/BERegistration.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			KpuP7Pn+USsxPtZyFQpYjDqOFjU=
			</data>
			<key>requirement</key>
			<string>identifier "com.incrediblebee.BERegistration" and anchor apple generic and certificate 1[field.1.2.840.113635.*********] /* exists */ and certificate leaf[field.1.2.840.113635.**********] /* exists */ and certificate leaf[subject.OU] = "3775D8NXN4"</string>
		</dict>
		<key>Frameworks/SFBCrashReporter.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			khMcb5SuU3PCBXunorCaA4O5Xjw=
			</data>
			<key>requirement</key>
			<string>identifier "org.sbooth.CrashReporter" and anchor apple generic and certificate 1[field.1.2.840.113635.*********] /* exists */ and certificate leaf[field.1.2.840.113635.**********] /* exists */ and certificate leaf[subject.OU] = "3775D8NXN4"</string>
		</dict>
		<key>Frameworks/Sparkle.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			MxLdGmuGyUihII+jpQMpHX/563g=
			</data>
			<key>requirement</key>
			<string>identifier "org.sparkle-project.Sparkle" and anchor apple generic and certificate 1[field.1.2.840.113635.*********] /* exists */ and certificate leaf[field.1.2.840.113635.**********] /* exists */ and certificate leaf[subject.OU] = "3775D8NXN4"</string>
		</dict>
		<key>Frameworks/UniversalDetector.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			VRM74uWXLU64KIrXfe4TsexnUV4=
			</data>
			<key>requirement</key>
			<string>identifier "org.mozilla.universalchardet" and anchor apple generic and certificate 1[field.1.2.840.113635.*********] /* exists */ and certificate leaf[field.1.2.840.113635.**********] /* exists */ and certificate leaf[subject.OU] = "3775D8NXN4"</string>
		</dict>
		<key>Frameworks/XADMaster.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			rqCwLRvWnE28I2dvj2qJfefHDj0=
			</data>
			<key>requirement</key>
			<string>identifier "de.dstoecker.xadmaster" and anchor apple generic and certificate 1[field.1.2.840.113635.*********] /* exists */ and certificate leaf[field.1.2.840.113635.**********] /* exists */ and certificate leaf[subject.OU] = "3775D8NXN4"</string>
		</dict>
		<key>Resources/7za</key>
		<dict>
			<key>hash2</key>
			<data>
			1IrhRB+Z8zGviHTh3+wHzPj9jSVerRjhgfr4IUh8SRs=
			</data>
		</dict>
		<key>Resources/7zip.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			uB8JUou7C/JGps/HH89Sv/ey+ZSSOvyJR/XxBZkjd+w=
			</data>
		</dict>
		<key>Resources/Acknowledgements.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			Z3SZMZFhLLFbtAMxeofGVqCjn7GtYbq9dHW8/2ITSBY=
			</data>
		</dict>
		<key>Resources/Assets.car</key>
		<dict>
			<key>hash2</key>
			<data>
			ViJc6JXWhbbnHSQoEPSJartF4zyCB/wS2CXWlAIGj2I=
			</data>
		</dict>
		<key>Resources/Credits.html</key>
		<dict>
			<key>hash2</key>
			<data>
			fNIoXHk9N1j0FFw5K+iNx8j1VxU1jl7SVUDxd9xf9Dw=
			</data>
		</dict>
		<key>Resources/License.html</key>
		<dict>
			<key>hash2</key>
			<data>
			7mI/1/V70Xz5z03MiRFVkO51zc0tY4mtitsQpkpdTqw=
			</data>
		</dict>
		<key>Resources/Updates.tiff</key>
		<dict>
			<key>hash2</key>
			<data>
			fysNZEEaGGNSTphCv75fjnVAgNQIQD4MU1dYRwmx7qs=
			</data>
		</dict>
		<key>Resources/adf.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			JaG6okGo8eTH3G8UkXTuG20hktBfzSB60k72lYRijoM=
			</data>
		</dict>
		<key>Resources/arch.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			gJ25RhegugrrzFMxiN+w8v6EQJwuhsXApdwG8/GCkC0=
			</data>
		</dict>
		<key>Resources/archiver.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			6UcGTuVBWiKzwv2Nrk6QaoHKyV8DyT1VjNUkSTDUtl0=
			</data>
		</dict>
		<key>Resources/arj.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			HV5e5ZHlwH38h1FrVx49UwyAW0GcD6uv2f34Z5akP0E=
			</data>
		</dict>
		<key>Resources/binary.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			hJoFXSKAgAO/lGf+aU92tYob16xhoKbOrTZ1gWnOCCc=
			</data>
		</dict>
		<key>Resources/cab.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			oLV6nB20YUgybOei6paVcpgjh4UGdRU5iIwcQbJczRU=
			</data>
		</dict>
		<key>Resources/chksplit.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			0FMVWZMLiCuCthGnSePiAqLANatmy1usVKJIgu35JxY=
			</data>
		</dict>
		<key>Resources/cpio.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			GUsfj+VZnax+kXWe4BkUL3FFX4xJvrSObYC86DCmY/Q=
			</data>
		</dict>
		<key>Resources/de.lproj/BEApplicationHelper.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			j3aVSGk/HvpYH9a6Bgli2Oa/mRYpHPoSVONjs9FDhks=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/BEBytesValueTransformer.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			8yFUSkqY6y4/XDxukRjFtp//vqhcIbwXAYYGNo4lMwE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/BEPreferencesWindowController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			65Y7vS5AoLoTbLNrdJ4wAZVOhvIcyZ84faTyIaW14iw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/BESandboxFileAuthenticator.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			aArNI6Ou0WMbponSJTdqL3U5B80BuXzOtdszfvyq7VY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/DropBox.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			H06OFwCT7u9TdEZpOG/XoqQ7fepqG6ifEyP7koyIZ4c=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/EditArchive.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			4gbQqe0cyRwM8fiFUm3zvPThSsmB5WfEmbHeJQDNJDw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/LicenseAgreement.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			5zTLjzcVxQ5cxy7VW19ZZpYBDSJo3jkhwE1pCNbXAb0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			JwB/LBwAop33cNiM9QjpDbWXxx57k3RhOr+qsM2VqH4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/MainMenu.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			dUp3g1xRB9pvy9KE/rBmgbyKkCy2PFJBfUB4eZGzW4w=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/MoveApplication.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			4Ule0M+JKvLcWLQ37vq/zu4ESsocNdZkYaSqsSuyX8U=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/MultipleFileOperationCompleted.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			50vSKac9e/ilTL7Q14hXZJanmE6/qc5lLCQQWVQFq2A=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/OperationCompleted.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			BHhn9rCWRw7pW7pBQ4N8WctZDjzzPpKyIexDA/3U2jc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/Password.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			1fY2mQ71yxzBofFWO6zjWi5frFV3adEFcy5EgdKeLQU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/Preferences.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			gkDE0yNwtUvyKXtceg8jvD3djCXdkEuw0+YeDZO79lU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/Progress.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			VdOefqvNLSh0sBM5qh+95ncXdfihdfFGCMa1YGktJn0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/Registration.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			pIQ5/zPCHuQgKSaz94/lco93hrIm6UvubzwtZzW7I20=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/SplitFile.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			E+A5YqLueH74rdLEdz0t6udEcQDhexLUVH0k1ZWzYl4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/Window.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			bRFrHNl8jF70n0L7az2o45gb61zCqWvXxGvk3V1zXsQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/dsa_pub.pem</key>
		<dict>
			<key>hash2</key>
			<data>
			ie75HyZO1kLET5LEC0ezeJUgGOXpHhnfiDKVDnjvCUY=
			</data>
		</dict>
		<key>Resources/en.lproj/Alert.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			gcmMm/DRsQ7bk4xd5iuWHYET42Z3/5in00Ahj2+byaE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Alert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			QbnFq6jy+0SgNGJm8cVHfz0vmqEe6/2D0eef255/9a8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/Archiver.Help.helpindex</key>
		<dict>
			<key>hash2</key>
			<data>
			jTQ5dpwDwkK/gSgfIeSSKWpDjDGh3eAhqe4X/dayWEU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/ArchiverHelp.html</key>
		<dict>
			<key>hash2</key>
			<data>
			wv6GL+c4hwYjfWdhhLEq0xjO0W+iMdgmcaaRw9wBNps=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/access_border2_bottom.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			sCSpMJ25xUGv190zsyWa0CvtR5spGWtVdPDhFdBnet0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/access_border2_bottomright.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			5XHyFz0oZypnwvWz+aoHbzl7sUvgB1e20WfFkcEBiuc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/access_border2_innerbottomleft.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			+hphZCNz3rudJIcSAVp4J8Lpl7p4a0l6jalFOZuNnp8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/access_border2_innerleft.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			0BCnI79EIybdPp4ReRhclYHBHOrp3zP4awM1+eskrFU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/access_border2_innertopleft.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			zkTBYV0v66fFSrKf4/4vfa/j5Hef/a1zoySmXwqog3M=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/access_border2_right.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			zrZ4dM2T/eygu+gg+dIJQWMbs7s3LSnNqcyPluK0pnc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/access_border2_top.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			f30Q+lJBzIqElxCUufwZyB4ck+jRVuvUYaW0MCLoySc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/access_border2_topright.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			BAXl6mmMjAh8y8IATLC+z8s/bX1ccncBC2pHzNP4Z4Q=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/access_border_bottom.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			wykp5u9FMEeeh06QERmkw9YyqAc5PsKuS1yNGSkBamE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/access_border_bottomleft.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			Tq9QZKFG/Q5yFddtmOSUVVmwEkOwDxbUFwcg9H+6fsc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/access_border_bottomright.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			W7VM4DiVMlbugnXB4orggE3MlSYxLXIAZfYveMBmKho=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/access_border_innerbottomleft.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			j6tRjazcdrRllguA/J1QMMfZKl+ABFLKkpZVAXiInUU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/access_border_innerbottomright.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			fXaCB27GCS1qi8LNZjsVK+klGpDyha93ql8NaXioOZk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/access_border_innerleft.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			3086spH1lWSxnKxk36wkDXSSqYfiGoOiamdFn+VRwsk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/access_border_innerright.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			O7rUnHsVpso237Ny6DIuz7Cj4mxAVIysu6dusuYTS14=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/access_border_innertopleft.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			exVWy3ucQNVE5sX7NNxzXHiAtzgm/s+Qo7dgMHHjvDo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/access_border_innertopright.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			oWKPruw+8U13W/6xP9wu9cboN1apjS6or/Yp4ewgfRY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/access_border_left.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			s445Xa6PQFxua/C2L5gPi1FXl0GRl6h2q7qtyWsaeV4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/access_border_right.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			keDV3bgUdtfZtl+SUFqNCpfC9aP84l41WvlCHVrUeqc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/access_border_top.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			O3CnwGi1NTT4MXWobzJzaIAD/Lm4OAJ0dKCJ58HC0c0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/access_border_topleft.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			gIRaBlLxhZpF0RNoKarwtEOdVx/RyCrrhue0xOhPmuM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/access_border_topright.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			K1kzrIDLK2XflpqcNcVV2UWABUt9DB+jd5L5g/XICIA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/arrow.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			ijycP1ZpIn3rCgn5wY7WB5PEUDL/eGcUPsf9N+62m4Y=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/ax_term.jpg</key>
		<dict>
			<key>hash2</key>
			<data>
			2nb43ytfd151oN0OOzvJG1VgKvwVt6c30Grm7g04qX8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/border_bottom.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			t5ZOi9Qc9JHylw0M+wbMGLuz+6BxcvZ9IUoq6k17Z08=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/border_bottomleft.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			APbDjXS6jobUvHkuclwLCWKvzZ3HKtLCJMGCsBN39Oc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/border_bottomright.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			RVpzoNw/iITDCIR6/aAYXzMNrX/2Hoe057E7tVWA+PA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/border_left.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			x/om3MdillR13Bs7kssRQXczC/QiaMY9fgNada0zSx4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/border_right.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			Kyfj+NmWdwZSFixUcyRMFU/pOhFRBuCeNJmI7f4fJa4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/border_top.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			buk/PYedJyZWLw3L7pFrbi4WeLitao+/ESrTVZ2X1M8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/border_topleft.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			NkXhmv0gbMlmKTqYoLRvs91NgR3731s45m3o4x0YmTg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/border_topright.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			R5+h1YnN/mi7IKXK1D9b1AifaZdIHo6Gt/VdlTaBOjQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/bullet.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			rOP3uQNTjm+pdlWaNkM5jaSXliihyJTrzAaNkyX56iw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/grid.tiff</key>
		<dict>
			<key>hash2</key>
			<data>
			jPHXIevR85YB1b7kUKTre/Phqi51n32HMb33DWbL5e8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/hlp24a.jpg</key>
		<dict>
			<key>hash2</key>
			<data>
			eLhbij5omL+LAqQTLTH9C9JTWU83iOLLe2ZEaOFsI0M=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/hlp24b.jpg</key>
		<dict>
			<key>hash2</key>
			<data>
			Eo06TOLa9uGC9tYGgpc2NzgaNESHvCSNafURlGVsofQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/icnfdbck.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			CYw9fJPvkfGFWPS6KB6+5g/Rp+TiQFrPs9n8sEcS9Rc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/icon.png</key>
		<dict>
			<key>hash2</key>
			<data>
			VcyDZl5vLQ45tMgaV234XGmTy8dohPXIeASLgtti/Is=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/iconSmall.png</key>
		<dict>
			<key>hash2</key>
			<data>
			/IY+YU0q6oW8mP9vMz9rUKhv+OXXunc5iNM0sQGOBR0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/index_circle.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			YVGo4cqyk7vLj5jOngRNaU3iG9vZ0JVSyx5NMoRpJlI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/index_circle_lefthalf.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			eVDItSV2eZMyUWHNW1wSO8+BfX+3evNXnfy2xtmuyBQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/index_circle_righthalf.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			f4Gtf3hgzxKTv6ct2GG3oRMuPkiYOBlSG6zKdfZPGd4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/ipodbk.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			FVER8UmnkIzLOT2bVLkNkaFajkd9NH5iIh6wk13Tth0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/list.tiff</key>
		<dict>
			<key>hash2</key>
			<data>
			n1pJiA4is762ilqqBth6ySxVSIj6dqWwheoFx2TJLdU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/orngbullet.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			/4qN5QcD4VESyNWdm+FVasNldCK2UfUrGQ/+Pm7FzjY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/rule.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			3yVkjf+b4m9qbnf1AURAfcm4c2ZOYugOv3wHKplXvBA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/step_1.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			906oy/07s8szSKFD6FdiPr7/Z+KIZJkKb1ogaFVpXMc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/step_10.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			/3/MsD20A01Dbi3ksV49deh/ARBPyvUAnub06e7tTuc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/step_2.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			TX24Nyi2SfvM34vlVSw3VKLON4k+OGEnpfCQDcPvyVM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/step_3.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			1q5m5U2beJrmWX8bW6dy/pg8Kx0E5tEHvountEZDcGc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/step_4.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			w9gsPw2vJ+Sr430OE4IdqYUxlGKuAxlTyq8gOTJVJs0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/step_5.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			NcYyPNGMXuSkVSSiSKy23ppQ0KijHWlxoH0bKyvym2Y=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/step_6.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			uC3IUygc5EATZ6Qfiyaq+DbU97gw8Y2N7E0ezxSCUEc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/step_7.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			c6qnGqCpLVfTan1X7VYS206j0B+26l1kCuDFYz621BI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/step_8.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			cmFKsEla5elowDHA9lfWXihFwLW2xxMoNmHBa71fPPo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/step_9.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			Z4WdF4opSErD/Jt9bIGECc1F7tNX7H5Rg++nNXleTpU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/table_mid_3.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			4PYBEUpcDR+V/p8owVhs7A4L6AjUcG7Eua0IgaMjiwM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/table_mid_5.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			ZaSjpZ3+pW7R106ALrl4DAcccooYI+EYVfakRN+ZzZw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/tableend_left_3px.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			NK02wvUVBrh9o0BeFjgqNDH6dOgJvnhUmDVMp1I2ggs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/tableend_left_5px.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			t0bcMr4IZw9qpEmC/bGmGakGkC/ma8rAVmbURnU93iQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/tableend_right_3px.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			MognoJDBzjQ/OhLWke75ixDbVyUCP1PPuRiHCE4w1EM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/tableend_right_5px.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			pdp7VKdzT/bCLVXxjX+a1N0jgj2vM3RpqVK6EKxHtnc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/gfx/trm2.jpg</key>
		<dict>
			<key>hash2</key>
			<data>
			5peYzF26VlUp2oINKW4Pu+nvq80HwYfpHepp/UhCCCU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/pgs/about.html</key>
		<dict>
			<key>hash2</key>
			<data>
			XAiSTLsyQp9nJNBdfUjCK7/DlT2c6tUXGhAPH6lBmTM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/pgs/p5421.html</key>
		<dict>
			<key>hash2</key>
			<data>
			5Mag1TJjOtTf1sWP1T+Fb0pLurC0+Kq+elCGEQTcYSE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/pgs/p5422.html</key>
		<dict>
			<key>hash2</key>
			<data>
			2guXvsaghreDa8Q9ahz8ymReG2EPf3YZNvgOGzh4aE0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/pgs/p5423.html</key>
		<dict>
			<key>hash2</key>
			<data>
			A6ZqRBvRbZCPJ1Pcs8Xn3ryHx5GlhwSu637domFYGRc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/pgs/p5424.html</key>
		<dict>
			<key>hash2</key>
			<data>
			Ms/auu6cN1dTyWBpljvKQ+NrYFUi3y+evEJSPH22w2Q=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/pgs/p5425.html</key>
		<dict>
			<key>hash2</key>
			<data>
			MgFPL5am1xu5u84yTFDCTUzPy6ULhA0oHJhU6T7tVmk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/pgs/p5426.html</key>
		<dict>
			<key>hash2</key>
			<data>
			aI7syHm141O+Zx4CWHaXP062NgDXuyABywXeFEKaqVA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/pgs/p5427.html</key>
		<dict>
			<key>hash2</key>
			<data>
			unvP7d9WB6SB1kUAHHTqh2o8UIhJUbEy5jRmJDn3Q5w=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/pgs/p5428.html</key>
		<dict>
			<key>hash2</key>
			<data>
			KVZy3QEunLUZ7X0QLs1dZS+UGmIY72OkyorH1JtuYYQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/pgs/p5429.html</key>
		<dict>
			<key>hash2</key>
			<data>
			73j3C/SR7aVk739FEqUdWk7CzjQgNXxIa4xTmrpdO0w=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/pgs/p5430.html</key>
		<dict>
			<key>hash2</key>
			<data>
			66dpBpXOc4ctVocGy53a7U353kfLQF3d8+onpMdrkaA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/pgs/p5431.html</key>
		<dict>
			<key>hash2</key>
			<data>
			pvrO9c6jWnkSCXe5SIlLWT0yX3RT6f5DqpgzDEH4RoU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/pgs/p5432.html</key>
		<dict>
			<key>hash2</key>
			<data>
			+lz/5ErJbGEZeKquF1QAEHvlcYsZziJasaickldN+nw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/pgs/p5433.html</key>
		<dict>
			<key>hash2</key>
			<data>
			uFHEjxxwjjhjkLjpTY2VVDf4wW+HtWh6jDq0dAVbXl0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/pgs/p5434.html</key>
		<dict>
			<key>hash2</key>
			<data>
			cvt3cK73K9hT5QfyA568PJbSUGeaPqVMigxDsbeafJA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/pgs/p5435.html</key>
		<dict>
			<key>hash2</key>
			<data>
			i2s+M3nATCAFw8A6/Kv+kKOzgSOVqzvI5jhzsnyRgwc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/pgs/p5436.html</key>
		<dict>
			<key>hash2</key>
			<data>
			YFYk0QEzBQstlhPqPOfNXyOEKDv2wZYt/GU/D2RdH/I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/pgs/p5437.html</key>
		<dict>
			<key>hash2</key>
			<data>
			ubdw5SdWIuqwqajb4QVW4WP54nf/4UQ4Ls7rNDOtlUk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/pgs/p5438.html</key>
		<dict>
			<key>hash2</key>
			<data>
			L/HU5s3umJBXPt7mW8UOB1RPN0p+onrYus7tUosMu/0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/pgs/p5439.html</key>
		<dict>
			<key>hash2</key>
			<data>
			NUMwiJIwsi/xkJZlHgXiAHprnpWzVr7pjvB84tJursI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/pgs/p5440.html</key>
		<dict>
			<key>hash2</key>
			<data>
			Bzaz62dmzus5KY+zFY7Exywzqj3NIVvobCSjMvBEZE8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/pgs/p5441.html</key>
		<dict>
			<key>hash2</key>
			<data>
			wuZc3WacVdCmaZ27g+FfvCtMDmyV/JbD6zhjAqOij6E=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/pgs/p5442.html</key>
		<dict>
			<key>hash2</key>
			<data>
			c8KgLn7+CJ7XSsgNsU4SydRdSThObbYM0BuY+lhX3AM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/pgs/p5443.html</key>
		<dict>
			<key>hash2</key>
			<data>
			R96sCMZuG2LpQZtP3zD5SR+imGvOdJlmzaQvYee0l8E=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/pgs/p5444.html</key>
		<dict>
			<key>hash2</key>
			<data>
			fr3vr2g2/u+1vIuV4l3jIolCtl8jZhsE+LlHx9AVeQE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/pgs/solvingProblems.html</key>
		<dict>
			<key>hash2</key>
			<data>
			5R98ZYx+Pj8lpYVDaDuA2a3IDEVSb5QJbMmoRxVlB0U=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/pgs/whatsNew.html</key>
		<dict>
			<key>hash2</key>
			<data>
			jvXcYMCqLGWMW0jSEYcANBzQth0jUn+XetQD9f6qNP4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/shrd/OpnApp.scpt</key>
		<dict>
			<key>hash2</key>
			<data>
			V+Ntv2tVt3IVREPZ3NAxI0WC59a1AHrcGENMD3fTYrU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/shrd/OpnAppBndID.scpt</key>
		<dict>
			<key>hash2</key>
			<data>
			2E4YphP3lG/PkurrkDw3Uevilvb4iGZIhxdEtMwEmmw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/shrd/OpnAppDict.scpt</key>
		<dict>
			<key>hash2</key>
			<data>
			9SzJSAIDghCnXaUC7tcZ+V7AyEgXN+vtGjGUKICHn3U=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/shrd/OpnFile.scpt</key>
		<dict>
			<key>hash2</key>
			<data>
			rUzcms3PFSL5yT4FceJWy5tFvW+PeqvkDQI8oo9f0A8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/shrd/OpnPrefsBndID.scpt</key>
		<dict>
			<key>hash2</key>
			<data>
			WwX358I0gb+OC4VpTA8Sdnns0PYPTMpY7M1HUvoM4aA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/shrd/opnbndsig.scpt</key>
		<dict>
			<key>hash2</key>
			<data>
			lYCnicuGiPCEHxKhrhLXPPU23rOPhVcnFCMlln3Z3s0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/sty/access_4box.css</key>
		<dict>
			<key>hash2</key>
			<data>
			N/9xwIb4uT5D0lg9NEpR/XPScVnVspulSzEjID2M4js=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/sty/genlist.html</key>
		<dict>
			<key>hash2</key>
			<data>
			nNyoOOVohtuayQbcIVbRzBthHJPtMbEQbvphuR+ZQT0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/sty/genlist_style.css</key>
		<dict>
			<key>hash2</key>
			<data>
			rCcowWgw4lFefOFPYUfpQnSFBm+AQoft6Bi0Xi44T3A=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/sty/index.css</key>
		<dict>
			<key>hash2</key>
			<data>
			iVeKJI1yX2DbQAd3dsDshyxQRG2qJIpFtYxgkiQIaJ4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/sty/launchpad.css</key>
		<dict>
			<key>hash2</key>
			<data>
			tQ1C3yQQ5zxav0fH5p40VZ0f0402eZeUDTysP7m5fYE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/sty/print.css</key>
		<dict>
			<key>hash2</key>
			<data>
			TGyJkrWxRuMeToVhdz4b1Q/UGbrrtYjwm07V+kInESQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/sty/ref.css</key>
		<dict>
			<key>hash2</key>
			<data>
			kUv5LPTpXMqvgBK47um0rBoTfo/Ap0ljG3Q1oXLG7bs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/sty/task_bulleted.css</key>
		<dict>
			<key>hash2</key>
			<data>
			FPestZ8RLWuC+M7Rvdz6S174NjggFkhxGT3wwX1h7kw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/sty/task_numbered.css</key>
		<dict>
			<key>hash2</key>
			<data>
			Xy6emWYgLNH9fCumuLShNbn0geIqPCmOrHWi6E8bhuk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/sty/task_plain.css</key>
		<dict>
			<key>hash2</key>
			<data>
			3OD4UaEw/z9mb53UzJSMhKWLV8B+slB/ZsWRmk1OxpY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Archiver.Help/xpgs/xall.html</key>
		<dict>
			<key>hash2</key>
			<data>
			9YTxUYazvbWlTVKmURyjydXTTqoRbpdB8RGB9qjwTbs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/BEApplicationHelper.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			gMXw47BHQ9/HuIy908t1OHz98sLcJK12fEC2CIJO6ew=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/BEBytesValueTransformer.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			1q5YB2kOgQRoS10UC8/Dluj2vp+emydT8JxPi7N3Kig=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/BEPreferencesWindowController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			P9dwrIq7YZ9f1mpHnZSvSAJ7jRwaQoUVZXItMiKCHr0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/BESandboxFileAuthenticator.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			ErAIndgP9lYg/MxkoMpdwE4lqVpJ44e5FxN5VOOG3ls=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/DropBox.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			MLndvvHUfbFwz0cI3E1AVSc4T7D+LMmQAqjzQMIzZ8M=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/DropBox.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			xtubYbekEY46X8+Q5b7lzdWytE/kjxk3UFwDn8zefvI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/EditArchive.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			UgyTVewMLbKRqQ+7h/qbS4J54HcI7VTr+rqHtQikdsI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/EditArchive.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			Yx1EzjEEfH0BAN1ABOggd7lksGcPBE0uvOkv5TxqJkQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/FileAnalysis.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			NaYpg+/VchcStIDA6fEIcBW8rGt/znALzxWffTJA7YA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/FileAnalysis.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			3wU+8P0gfqy9Y07D+nvNLp6v7ItoqMdXESFpDqDgpXE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/FileOptions.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			Wzatrda7/166s7STnvRNCyqcvUXjA5tgwM/myS8qT9Q=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/FileOptions.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			7puHvVaZbysCfeu1Qio4tKHP9Vs3bVvZ3sNbaAbrT6U=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/LicenseAgreement.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			a9aiy12FBlFCCKqJHdISmkyARi9ETL7uybtUL8pNcuU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/LicenseAgreement.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			3h5IuopPQsqr1+YZQ6WLHgiEt2iRh7n8VWOaU3rFwdE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			1AVfQYRDDulYY2mq70DVnRFyrEKhrN4SEQoJPq9vguY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/MainMenu.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			pNNpe3k5PKI6wGHlER+vS9N9Wwil1NSQG/7YDs2I/l0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/MainMenu.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			tsZRuYvdKdKar77nLob0I4/GSfIrEPQh6OVtXmhZEhs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/MultipleFileOperationCompleted.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			sbCy7Xtz3GsOligCg3qjO62KBOTI0GWDsO5eyq0czxE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/MultipleFileOperationCompleted.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			h8/mh5WK70NDUM9dKsSKEgaGUv3ab+CsAfWEWAgRF+o=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/OperationCompleted.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			VB1gLvQGvD7WoliwefrhRAwy++VpB002rzM5MGhs/nM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/OperationCompleted.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			Uf2EBxUICE012CezTKzZ1p+yJOcGM0arvAaQmk2g2gE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Password.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			snYyW+0qk9Y1YAmomlAQiOM5gbEXX9XVCFS8T74zJnc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Password.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			xIZ5ry2H6zMbcj5KqqZxN9UkqbcpbV2OsrU9Lf6Myoc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Preferences.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			278U1s2MuVCMp7mA7wn/WBEXPUMDv+CRQ7KosZLTbW8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Preferences.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			RA8fQazVvaxNIiATylMME/8wApbAFSyi9ow/DGVxmtU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Preview.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			AwvtrKZSQTsYUs9Ng9hhq30d9YgXBThPq3Xh0Cjo6zU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Preview.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			lvufmAIfER9JASeCi/GPZ27SJheVsd+GtBo/+fFhY18=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Progress.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			02KuPClYQrmsMNLkxWkkQM/G4nwJCnYMZmH/z7Dw6iQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Progress.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			Q1KlJr+qmCsVSUKQTwzGsaxSmgF/HPDMnWN+6q+MOMo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/SplitFile.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			1by7w8gvE7J3A1VmW7QbuNNB/tdurIIp4L3siV/duIo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/SplitFile.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			soxdwBiJnJSWDZS83kLYdsF8nkiaDbcbgQ3iIDy/1OY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Window.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			XELdT+kofotLu5hmRPDKiiRw5VC9SWIphDOsgc9uuKk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/Window.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			rxGo9hQxhYNYhu0MrzI1xehhwYsjsPxOmMF2x0W7U28=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/BEApplicationHelper.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			/bHhiIIranZBgAoBb6nZO20wSnvOKWWItU0vgpsjDT4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/BEBytesValueTransformer.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			/1voLJWbnhPvndAQcdDjqlccNdu3BWmyf5nTO6uofk8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/BEPreferencesWindowController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			0L7iKc9vU0X9PXf8efv/5N40HFt1tr5ichKHvgRJyW0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/BESandboxFileAuthenticator.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			t2X+YZ+dTJ2DfW4C6xaqi02IEgns1qWlwBE/weNcHIw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/DropBox.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			333THetX3DMoio2f6oHa9HNenPeWIbPrx4yaX3kEerI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/EditArchive.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			LKeQgcfAssnAn3c9M2D+w1FAWVxgBhGyN0twq80tPfE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/LicenseAgreement.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			TWbu52x93FTJXRV0kA9Idmmm8ReuT+Ota06shPWiSyY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			wXRXmaU/sfrlrHKLvb6BElcDo2sEn1GStCWt8Gf0jN8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/MainMenu.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			WZOxDsOm4e9b3NFNJXSeBJZQMnMfdopXHEhGg2c6dwI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/MoveApplication.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			3JEMIQ5yWYWW8/b2VxE4wkmKiyBBZH4jLYLk3sisEwY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/MultipleFileOperationCompleted.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			Kn4xhWtwObouPMB1CM+11Tuxm3LaB0JSHN9afxntkaY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/OperationCompleted.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			Ru7iIHUnqKdVJc8KlLrH2H0LsJy/VpLElTaC89pVVqg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/Password.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			ufUBmZeC23qkMLHCmLCZQutLrFUXc9m6JxM5XN8+MqQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/Preferences.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			k+yYkgJYAJSqOEh8DMBC1YGMjg6KvlhuMRjtexTDI94=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/Progress.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			fgRDMie2Aqh2XVuOeCTdZynoLKnX4Uo//Lg/YgpVdDI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/Registration.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			j/LLvVOZ95hiERt8abvEESlLYLDOu39D6OllHFnfaro=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/SplitFile.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			LZummsG0youZY+EaP0S3V0piozTz3PwAL1pShHFk1XM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/Window.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			VctZf1OkKXfHzuFxaClMMgg6QXUTgArYgNhR8q9PRjo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/filetypes.png</key>
		<dict>
			<key>hash2</key>
			<data>
			EfcJZuanDcZ/goLnYJ0Yl74y1bZi8VpzRAWSd/myKmU=
			</data>
		</dict>
		<key>Resources/horizontal_line.tiff</key>
		<dict>
			<key>hash2</key>
			<data>
			N1pwxqZ4C11OLj0SPQESv3OgtKucou9lcGS6cDJ7BUg=
			</data>
		</dict>
		<key>Resources/hqx.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			SZXph2xxCAjPccNPJ2qX4yTR6cMn+R/x7zTHesKulNI=
			</data>
		</dict>
		<key>Resources/icon.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			cfyZ+/NStCOrs9teHIdbsXFqNZJ7UHV3bOoOYFF46LE=
			</data>
		</dict>
		<key>Resources/jar.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			HWMudaqi2bijMxMJB2HP4O41qrMxBI0kJEC6F/MhNtw=
			</data>
		</dict>
		<key>Resources/lha.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			f+CpippUsGtUrmOyTZ3z6FoC8NzbzSzYRCe8euS4/1U=
			</data>
		</dict>
		<key>Resources/pax.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			ZW3ZKN/ScZEZjD/q4Ig7X4nRrenyu0CS3kWFCtVUmvc=
			</data>
		</dict>
		<key>Resources/rar.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			s0Sgf3MvrnYm2/ehPIzDvg6PZjmdcTprpGpViYT9UFM=
			</data>
		</dict>
		<key>Resources/rpm.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			yq+SLRjCaVFT3F6c5rN1vzZSDZAHjE10GjuNiTGd5D4=
			</data>
		</dict>
		<key>Resources/ru.lproj/DropBox.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			iLOqZG/S2p0WnLgGpURNsWKkEroVy1Y4D0w6Fon8Dx4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ru.lproj/EditArchive.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			WanYfynJcymRJGziOf8gcgwKp9nLnWQNL1fsxwIpCnc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ru.lproj/LicenseAgreement.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			FRNt8FS43HbtKcZZ/Y3AjWS94KUOQ/LS3fBiXmwiNvg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ru.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			kh/o1Fa0910LLR1BPstNTxLiTgLV/paOCPt+/ctrq40=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ru.lproj/MainMenu.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			wl1UHtxvCJEH8pAscCDqGGlU0pPRBzn6HmLaz6F0Oq4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ru.lproj/MoveApplication.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			f35sTRF1rgZnBy7W2XwsIU8TRa9NdO0Ch78l4/8A1Q8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ru.lproj/MultipleFileOperationCompleted.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			AiMJhO+cn0OkMvdJnkZeegfLbcFYuVlx1vycLRT1wVI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ru.lproj/OperationCompleted.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			R5MQEfO/qnwPEp7+lKq7DeZe0f2w5FvkPXE3ld9QGqs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ru.lproj/Password.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			WHxno+eC0jkbUY+vUvg3XmoryUfEdG5aVkzUL991nPo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ru.lproj/Preferences.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			3+RI+EU9KtlZCMzMfSstVgVPypUoOYd0qexhzN6yMyo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ru.lproj/Progress.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			qv3X00eUkwbuOHxijJkmiBxfGvU+ourVfrsbUAa396c=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ru.lproj/Registration.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			hbhxFRH49QEgusZLGynH9RFo+/nA1CbQnxwfWF6EqsY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ru.lproj/SplitFile.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			V/tpvsgRx+4VF+SXwnPNbkA3KGoB+OBejVe3fBz0+h8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ru.lproj/Window.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			zbQJeIUeoViNsFtdBTmnwcZ7STywtMZm6c1R0I3s8nM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sit.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			RAW/ragSXwcPU8IYLtkh7DQNEMo0FuxZQ8qV8T7ifAc=
			</data>
		</dict>
		<key>Resources/split.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			oQ5G5Iga5NJeIexFpj6ioraymByk9ojBXQEkcDi99dI=
			</data>
		</dict>
		<key>Resources/tar-bz.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			JrNHaBvhnnhODwSwXqwZNSBk63BYvfns/s8ZGs1yZcc=
			</data>
		</dict>
		<key>Resources/tar-gz.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			WlhZ00efKPMbB/HYCJtPU088sC0d0xuNYZYr5sZT+1Y=
			</data>
		</dict>
		<key>Resources/tar-z.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			hJyH8+SoL5S2BE8zDnTGzlRyFSAcwrscTxu0MrL25yY=
			</data>
		</dict>
		<key>Resources/tar.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			QLA3S7DYwDGwYLMQbTN4xyxZI9FrG2k8sARb6ke268M=
			</data>
		</dict>
		<key>Resources/tbz.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			y9p6rVbPMFwBq+ebWk6jIZAkBvCJbpOKdaaAmg91VgQ=
			</data>
		</dict>
		<key>Resources/tgz.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			ZfX8GWIcLJIls11z/IzvTPKM8y9BZ0zRhczNGqtQlyg=
			</data>
		</dict>
		<key>Resources/update.png</key>
		<dict>
			<key>hash2</key>
			<data>
			8HtZvSqrYjo3o6N+5jAcjGwS5IyoyekUtwIfjneN2KE=
			</data>
		</dict>
		<key>Resources/xar.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			3gGhEsRV40RTC9AWM9j81CtN8oBBaepl4KNAc3MEraU=
			</data>
		</dict>
		<key>Resources/zip.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			13Jjj2tC6NomESUOtbGgScmz3b23Co9VTg7aV55taLk=
			</data>
		</dict>
		<key>Resources/zsplit.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			jCMWwUtP3w5YaZb+kmZUtAkKqsJDbFfHfRIgGM2ER2E=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
