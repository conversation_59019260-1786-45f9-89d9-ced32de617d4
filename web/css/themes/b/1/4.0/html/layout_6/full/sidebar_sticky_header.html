<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
	<title>Limitless - Responsive Web Application Kit by <PERSON></title>

	<!-- Global stylesheets -->
	<link href="../../../assets/fonts/inter/inter.css" rel="stylesheet" type="text/css">
	<link href="../../../assets/icons/phosphor/styles.min.css" rel="stylesheet" type="text/css">
	<link href="assets/css/ltr/all.min.css" id="stylesheet" rel="stylesheet" type="text/css">
	<!-- /global stylesheets -->

	<!-- Core JS files -->
	<script src="../../../assets/demo/demo_configurator.js"></script>
	<script src="../../../assets/js/bootstrap/bootstrap.bundle.min.js"></script>
	<!-- /core JS files -->

	<!-- Theme JS files -->
	<script src="../../../assets/js/vendor/ui/prism.min.js"></script>

	<script src="assets/js/app.js"></script>
	<!-- /theme JS files -->

</head>

<body>

	<!-- Main navbar -->
	<div class="navbar navbar-expand-xl navbar-static shadow">
		<div class="container-fluid">
			<div class="d-flex d-xl-none me-2">
				<button type="button" class="navbar-toggler sidebar-mobile-main-toggle rounded-pill">
					<i class="ph-list"></i>
				</button>
			</div>

			<div class="navbar-brand flex-1">
				<a href="index.html" class="d-inline-flex align-items-center">
					<img src="../../../assets/images/logo_icon.svg" alt="">
					<img src="../../../assets/images/logo_text_dark.svg" class="d-none d-sm-inline-block h-16px invert-dark ms-3" alt="">
				</a>
			</div>

			<div class="d-flex w-100 w-xl-auto overflow-auto overflow-xl-visible scrollbar-hidden border-top border-top-xl-0 order-1 order-xl-0 pt-2 pt-xl-0 mt-2 mt-xl-0">
				<ul class="nav gap-1 justify-content-center flex-nowrap flex-xl-wrap mx-auto">
					<li class="nav-item">
						<a href="index.html" class="navbar-nav-link rounded">
							<i class="ph-house me-2"></i>
							Home
						</a>
					</li>

					<li class="nav-item nav-item-dropdown">
						<a href="#" class="navbar-nav-link dropdown-toggle rounded active" data-bs-toggle="dropdown" data-bs-auto-close="outside">
							<i class="ph-rows me-2"></i>
							Navigation
						</a>

						<div class="dropdown-menu p-0">
							<div class="d-xl-flex">
								<div class="d-flex flex-row flex-xl-column bg-light overflow-auto overflow-xl-visible rounded-top rounded-top-xl-0 rounded-start-xl">
									<div class="flex-1 border-bottom border-bottom-xl-0 p-2 p-xl-3">
										<div class="fw-bold border-bottom d-none d-xl-block pb-2 mb-2">Navigation</div>
										<ul class="nav nav-pills flex-xl-column flex-nowrap text-nowrap justify-content-center wmin-xl-300" role="tablist">
											<li class="nav-item" role="presentation">
												<a href="#tab_page" class="nav-link rounded" data-bs-toggle="tab" aria-selected="false" role="tab">
													<i class="ph-layout me-2"></i>
													Page
													<i class="ph-arrow-right nav-item-active-indicator d-none d-xl-inline-block ms-auto"></i>
												</a>
											</li>
											<li class="nav-item" role="presentation">
												<a href="#tab_navbars" class="nav-link rounded" data-bs-toggle="tab" aria-selected="false" tabindex="-1" role="tab">
													<i class="ph-rows me-2"></i>
													Navbars
													<i class="ph-arrow-right nav-item-active-indicator d-none d-xl-inline-block ms-auto"></i>
												</a>
											</li>
											<li class="nav-item" role="presentation">
												<a href="#tab_sidebar_types" class="nav-link rounded" data-bs-toggle="tab" aria-selected="false" tabindex="-1" role="tab">
													<i class="ph-columns me-2"></i>
													Sidebar types
													<i class="ph-arrow-right nav-item-active-indicator d-none d-xl-inline-block ms-auto"></i>
												</a>
											</li>
											<li class="nav-item" role="presentation">
												<a href="#tab_sidebar_content" class="nav-link rounded active" data-bs-toggle="tab" aria-selected="true" tabindex="-1" role="tab">
													<i class="ph-sidebar-simple me-2"></i>
													Sidebar content
													<i class="ph-arrow-right nav-item-active-indicator d-none d-xl-inline-block ms-auto"></i>
												</a>
											</li>
											<li class="nav-item" role="presentation">
												<a href="#tab_navigation" class="nav-link rounded" data-bs-toggle="tab" aria-selected="false" tabindex="-1" role="tab">
													<i class="ph-list-dashes me-2"></i>
													Navigation
													<i class="ph-arrow-right nav-item-active-indicator d-none d-xl-inline-block ms-auto"></i>
												</a>
											</li>
										</ul>
									</div>
								</div>

								<div class="tab-content flex-xl-1">
									<div class="tab-pane dropdown-scrollable-xl fade p-3" id="tab_page" role="tabpanel">
										<div class="row">
											<div class="col-lg-4 mb-3 mb-lg-0">
												<div class="fw-bold border-bottom pb-2 mb-2">Sections</div>
												<a href="layout_no_header.html" class="dropdown-item rounded">No header</a>
												<a href="layout_no_footer.html" class="dropdown-item rounded">No footer</a>
												<a href="layout_fixed_header.html" class="dropdown-item rounded">Fixed header</a>
												<a href="layout_fixed_footer.html" class="dropdown-item rounded">Fixed footer</a>
											</div>

											<div class="col-lg-4 mb-3 mb-lg-0">
												<div class="fw-bold border-bottom pb-2 mb-2">Sidebars</div>
												<a href="layout_2_sidebars_1_side.html" class="dropdown-item rounded">2 sidebars on 1 side</a>
												<a href="layout_2_sidebars_2_sides.html" class="dropdown-item rounded">2 sidebars on 2 sides</a>
												<a href="layout_3_sidebars.html" class="dropdown-item rounded">3 sidebars</a>
											</div>

											<div class="col-lg-4">
												<div class="fw-bold border-bottom pb-2 mb-2">Layout</div>
												<a href="layout_static.html" class="dropdown-item rounded">Static layout</a>
												<a href="layout_boxed_page.html" class="dropdown-item rounded">Boxed page</a>
												<a href="layout_liquid_content.html" class="dropdown-item rounded">Liquid content</a>
											</div>
										</div>
									</div>

									<div class="tab-pane dropdown-scrollable-xl fade p-3" id="tab_navbars" role="tabpanel">
										<div class="row">
											<div class="col-lg-3 mb-3 mb-lg-0">
												<div class="fw-bold border-bottom pb-2 mb-2">Single</div>
												<a href="navbar_single_top_static.html" class="dropdown-item rounded">Top static</a>
												<a href="navbar_single_top_fixed.html" class="dropdown-item rounded">Top fixed</a>
												<a href="navbar_single_bottom_static.html" class="dropdown-item rounded">Bottom static</a>
												<a href="navbar_single_bottom_fixed.html" class="dropdown-item rounded">Bottom fixed</a>
											</div>

											<div class="col-lg-3 mb-3 mb-lg-0">
												<div class="fw-bold border-bottom pb-2 mb-2">Multiple</div>
												<a href="navbar_multiple_top_static.html" class="dropdown-item rounded">Top static</a>
												<a href="navbar_multiple_top_fixed.html" class="dropdown-item rounded">Top fixed</a>
												<a href="navbar_multiple_bottom_static.html" class="dropdown-item rounded">Bottom static</a>
												<a href="navbar_multiple_bottom_fixed.html" class="dropdown-item rounded">Bottom fixed</a>
												<a href="navbar_multiple_top_bottom_fixed.html" class="dropdown-item rounded">Top and bottom fixed</a>
											</div>

											<div class="col-lg-3 mb-3 mb-lg-0">
												<div class="fw-bold border-bottom pb-2 mb-2">Content</div>
												<a href="navbar_component_single.html" class="dropdown-item rounded">Single navbar</a>
												<a href="navbar_component_multiple.html" class="dropdown-item rounded">Multiple navbars</a>
											</div>

											<div class="col-lg-3">
												<div class="fw-bold border-bottom pb-2 mb-2">Other</div>
												<a href="navbar_colors.html" class="dropdown-item rounded">Color options</a>
												<a href="navbar_sizes.html" class="dropdown-item rounded">Sizing options</a>
												<a href="navbar_components.html" class="dropdown-item rounded">Navbar components</a>
											</div>
										</div>
									</div>

									<div class="tab-pane dropdown-scrollable-xl fade p-3" id="tab_sidebar_types" role="tabpanel">
										<div class="row">
											<div class="col-lg-3 mb-3 mb-lg-0">
												<div class="fw-bold border-bottom pb-2 mb-2">Main</div>
												<a href="sidebar_default_resizable.html" class="dropdown-item rounded">Resizable</a>
												<a href="sidebar_default_resized.html" class="dropdown-item rounded">Resized</a>
												<a href="sidebar_default_collapsible.html" class="dropdown-item rounded">Collapsible</a>
												<a href="sidebar_default_collapsed.html" class="dropdown-item rounded">Collapsed</a>
												<a href="sidebar_default_hideable.html" class="dropdown-item rounded">Hideable</a>
												<a href="sidebar_default_hidden.html" class="dropdown-item rounded">Hidden</a>
												<a href="sidebar_default_color_dark.html" class="dropdown-item rounded">Dark color</a>
											</div>

											<div class="col-lg-3 mb-3 mb-lg-0">
												<div class="fw-bold border-bottom pb-2 mb-2">Secondary</div>
												<a href="sidebar_secondary_collapsible.html" class="dropdown-item rounded">Collapsible</a>
												<a href="sidebar_secondary_collapsed.html" class="dropdown-item rounded">Collapsed</a>
												<a href="sidebar_secondary_hideable.html" class="dropdown-item rounded">Hideable</a>
												<a href="sidebar_secondary_hidden.html" class="dropdown-item rounded">Hidden</a>
												<a href="sidebar_secondary_color_dark.html" class="dropdown-item rounded">Dark color</a>
											</div>

											<div class="col-lg-3 mb-3 mb-lg-0">
												<div class="fw-bold border-bottom pb-2 mb-2">Right</div>
												<a href="sidebar_right_collapsible.html" class="dropdown-item rounded">Collapsible</a>
												<a href="sidebar_right_collapsed.html" class="dropdown-item rounded">Collapsed</a>
												<a href="sidebar_right_hideable.html" class="dropdown-item rounded">Hideable</a>
												<a href="sidebar_right_hidden.html" class="dropdown-item rounded">Hidden</a>
												<a href="sidebar_right_color_dark.html" class="dropdown-item rounded">Dark color</a>
											</div>

											<div class="col-lg-3">
												<div class="fw-bold border-bottom pb-2 mb-2">Content</div>
												<a href="sidebar_content_left.html" class="dropdown-item rounded">Left aligned</a>
												<a href="sidebar_content_left_stretch.html" class="dropdown-item rounded">Left stretched</a>
												<a href="sidebar_content_left_sections.html" class="dropdown-item rounded">Left sectioned</a>
												<a href="sidebar_content_right.html" class="dropdown-item rounded">Right aligned</a>
												<a href="sidebar_content_right_stretch.html" class="dropdown-item rounded">Right stretched</a>
												<a href="sidebar_content_right_sections.html" class="dropdown-item rounded">Right sectioned</a>
												<a href="sidebar_content_color_dark.html" class="dropdown-item rounded">Dark color</a>
											</div>
										</div>
									</div>

									<div class="tab-pane dropdown-scrollable-xl fade show active p-3" id="tab_sidebar_content" role="tabpanel">
										<div class="row">
											<div class="col-lg-6 mb-3 mb-lg-0">
												<div class="fw-bold border-bottom pb-2 mb-2">Sticky areas</div>
												<a href="sidebar_sticky_header.html" class="dropdown-item rounded active">Header</a>
												<a href="sidebar_sticky_footer.html" class="dropdown-item rounded">Footer</a>
												<a href="sidebar_sticky_header_footer.html" class="dropdown-item rounded">Header and footer</a>
												<a href="sidebar_sticky_custom.html" class="dropdown-item rounded">Custom elements</a>
											</div>

											<div class="col-lg-6">
												<div class="fw-bold border-bottom pb-2 mb-2">Other</div>
												<a href="sidebar_components.html" class="dropdown-item rounded">Sidebar components</a>
											</div>
										</div>
									</div>

									<div class="tab-pane dropdown-scrollable-xl fade p-3" id="tab_navigation" role="tabpanel">
										<div class="row">
											<div class="col-lg-6 mb-3 mb-lg-0">
												<div class="fw-bold border-bottom pb-2 mb-2">Vertical</div>
												<a href="navigation_vertical_styles.html" class="dropdown-item rounded">Navigation styles</a>
												<a href="navigation_vertical_collapsible.html" class="dropdown-item rounded">Collapsible menu</a>
												<a href="navigation_vertical_accordion.html" class="dropdown-item rounded">Accordion menu</a>
												<a href="navigation_vertical_bordered.html" class="dropdown-item rounded">Bordered navigation</a>
												<a href="navigation_vertical_right_icons.html" class="dropdown-item rounded">Right icons</a>
												<a href="navigation_vertical_badges.html" class="dropdown-item rounded">Badges</a>
												<a href="navigation_vertical_disabled.html" class="dropdown-item rounded">Disabled items</a>
											</div>

											<div class="col-lg-6">
												<div class="fw-bold border-bottom pb-2 mb-2">Horizontal</div>
												<a href="navigation_horizontal_styles.html" class="dropdown-item rounded">Navigation styles</a>
												<a href="navigation_horizontal_elements.html" class="dropdown-item rounded">Navigation elements</a>
												<a href="navigation_horizontal_tabs.html" class="dropdown-item rounded">Tabbed navigation</a>
												<a href="navigation_horizontal_disabled.html" class="dropdown-item rounded">Disabled items</a>
												<a href="navigation_horizontal_mega.html" class="dropdown-item rounded">Mega menu</a>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</li>

					<li class="nav-item nav-item-dropdown-xl dropdown">
						<a href="#" class="navbar-nav-link dropdown-toggle rounded" data-bs-toggle="dropdown">
							<i class="ph-note-blank me-2"></i>
							Starter kit
						</a>

						<div class="dropdown-menu">
							<div class="dropdown-submenu dropdown-submenu-start">
								<a href="#" class="dropdown-item dropdown-toggle">
									<i class="ph-columns me-2"></i>
									Sidebars
								</a>
								<div class="dropdown-menu">
									<a href="../seed/layout_2_sidebars_1_side.html" class="dropdown-item rounded">2 sidebars on 1 side</a>
									<a href="../seed/layout_2_sidebars_2_sides.html" class="dropdown-item rounded">2 sidebars on 2 sides</a>
									<a href="../seed/layout_3_sidebars.html" class="dropdown-item rounded">3 sidebars</a>
								</div>
							</div>
							<div class="dropdown-submenu dropdown-submenu-start">
								<a href="#" class="dropdown-item dropdown-toggle">
									<i class="ph-rows me-2"></i>
									Sections
								</a>
								<div class="dropdown-menu">
									<a href="../seed/layout_no_header.html" class="dropdown-item rounded">No header</a>
									<a href="../seed/layout_no_footer.html" class="dropdown-item rounded">No footer</a>
									<a href="../seed/layout_fixed_header.html" class="dropdown-item rounded">Fixed header</a>
									<a href="../seed/layout_fixed_footer.html" class="dropdown-item rounded">Fixed footer</a>
								</div>
							</div>
							<div class="dropdown-divider"></div>
							<a href="../seed/layout_static.html" class="dropdown-item rounded">Static layout</a>
							<a href="../seed/layout_boxed_page.html" class="dropdown-item rounded">Boxed page</a>
							<a href="../seed/layout_liquid_content.html" class="dropdown-item rounded">Liquid content</a>
						</div>
					</li>

					<li class="nav-item nav-item-dropdown-xl dropdown">
						<a href="#" class="navbar-nav-link dropdown-toggle rounded" data-bs-toggle="dropdown">
							<i class="ph-arrows-clockwise me-2"></i>
							Switch
						</a>

						<div class="dropdown-menu dropdown-menu-end">
							<div class="dropdown-submenu dropdown-submenu-start">
								<a href="#" class="dropdown-item dropdown-toggle">
									<i class="ph-layout me-2"></i>
									Layouts
								</a>
								<div class="dropdown-menu">
									<a href="../../layout_1/full/index.html" class="dropdown-item">Default layout</a>
									<a href="../../layout_2/full/index.html" class="dropdown-item">Layout 2</a>
									<a href="../../layout_3/full/index.html" class="dropdown-item">Layout 3</a>
									<a href="../../layout_4/full/index.html" class="dropdown-item">Layout 4</a>
									<a href="../../layout_5/full/index.html" class="dropdown-item">Layout 5</a>
									<a href="index.html" class="dropdown-item active">Layout 6</a>
									<a href="../../layout_7/full/index.html" class="dropdown-item disabled">
										Layout 7
										<span class="opacity-75 fs-sm ms-auto">Coming soon</span>
									</a>
								</div>
							</div>
							<div class="dropdown-submenu dropdown-submenu-start">
								<a href="#" class="dropdown-item dropdown-toggle">
									<i class="ph-swatches me-2"></i>
									Themes
								</a>
								<div class="dropdown-menu">
									<a href="index.html" class="dropdown-item active">Default</a>
									<a href="../../../LTR/material/full/index.html" class="dropdown-item disabled">
										Material
										<span class="opacity-75 fs-sm ms-auto">Coming soon</span>
									</a>
									<a href="../../../LTR/clean/full/index.html" class="dropdown-item disabled">
										Clean
										<span class="opacity-75 fs-sm ms-auto">Coming soon</span>
									</a>
								</div>
							</div>
						</div>
					</li>
				</ul>
			</div>

			<ul class="nav gap-1 flex-xl-1 justify-content-end order-0 order-xl-1">
				<li class="nav-item nav-item-dropdown-xl dropdown">
					<a href="#" class="navbar-nav-link navbar-nav-link-icon rounded-pill" data-bs-toggle="dropdown">
						<i class="ph-squares-four"></i>
					</a>

					<div class="dropdown-menu dropdown-menu-end dropdown-menu-scrollable-sm wmin-xl-600 p-0">
						<div class="d-flex align-items-center border-bottom p-3">
							<h6 class="mb-0">Browse apps</h6>
							<a href="#" class="ms-auto">
								View all
								<i class="ph-arrow-circle-right ms-1"></i>
							</a>
						</div>

						<div class="row row-cols-1 row-cols-sm-2 g-0">
							<div class="col">
								<button type="button" class="dropdown-item text-wrap h-100 align-items-start border-end-sm border-bottom p-3">
									<div>
										<img src="../../../assets/images/demo/logos/1.svg" class="h-40px mb-2" alt="">
										<div class="fw-semibold my-1">Customer data platform</div>
										<div class="text-muted">Unify customer data from multiple sources</div>
									</div>
								</button>
							</div>

							<div class="col">
								<button type="button" class="dropdown-item text-wrap h-100 align-items-start border-bottom p-3">
									<div>
										<img src="../../../assets/images/demo/logos/2.svg" class="h-40px mb-2" alt="">
										<div class="fw-semibold my-1">Data catalog</div>
										<div class="text-muted">Discover, inventory, and organize data assets</div>
									</div>
								</button>
							</div>

							<div class="col">
								<button type="button" class="dropdown-item text-wrap h-100 align-items-start border-end-sm border-bottom border-bottom-sm-0 rounded-bottom-start p-3">
									<div>
										<img src="../../../assets/images/demo/logos/3.svg" class="h-40px mb-2" alt="">
										<div class="fw-semibold my-1">Data governance</div>
										<div class="text-muted">The collaboration hub and data marketplace</div>
									</div>
								</button>
							</div>

							<div class="col">
								<button type="button" class="dropdown-item text-wrap h-100 align-items-start rounded-bottom-end p-3">
									<div>
										<img src="../../../assets/images/demo/logos/4.svg" class="h-40px mb-2" alt="">
										<div class="fw-semibold my-1">Data privacy</div>
										<div class="text-muted">Automated provisioning of non-production datasets</div>
									</div>
								</button>
							</div>
						</div>
					</div>
				</li>

				<li class="nav-item">
					<a href="#" class="navbar-nav-link navbar-nav-link-icon rounded-pill" data-bs-toggle="offcanvas" data-bs-target="#notifications">
						<i class="ph-bell"></i>
						<span class="badge bg-yellow text-black position-absolute top-0 end-0 translate-middle-top zindex-1 rounded-pill mt-1 me-1">2</span>
					</a>
				</li>

				<li class="nav-item nav-item-dropdown-xl dropdown">
					<a href="#" class="navbar-nav-link align-items-center rounded-pill p-1" data-bs-toggle="dropdown">
						<div class="status-indicator-container">
							<img src="../../../assets/images/demo/users/face11.jpg" class="w-32px h-32px rounded-pill" alt="">
							<span class="status-indicator bg-success"></span>
						</div>
						<span class="d-none d-md-inline-block mx-md-2">Victoria</span>
					</a>

					<div class="dropdown-menu dropdown-menu-end">
						<a href="#" class="dropdown-item">
							<i class="ph-user-circle me-2"></i>
							My profile
						</a>
						<a href="#" class="dropdown-item">
							<i class="ph-currency-circle-dollar me-2"></i>
							My subscription
						</a>
						<a href="#" class="dropdown-item">
							<i class="ph-shopping-cart me-2"></i>
							My orders
						</a>
						<a href="#" class="dropdown-item">
							<i class="ph-envelope-open me-2"></i>
							My inbox
							<span class="badge bg-primary rounded-pill ms-auto">26</span>
						</a>
						<div class="dropdown-divider"></div>
						<a href="#" class="dropdown-item">
							<i class="ph-gear me-2"></i>
							Account settings
						</a>
						<a href="#" class="dropdown-item">
							<i class="ph-sign-out me-2"></i>
							Logout
						</a>
					</div>
				</li>
			</ul>
		</div>
	</div>
	<!-- /main navbar -->


	<!-- Page content -->
	<div class="page-content">

		<!-- Main sidebar -->
		<div class="sidebar sidebar-main sidebar-expand-lg">

			<!-- Expand button -->
			<button type="button" class="btn btn-sidebar-expand sidebar-control sidebar-main-toggle h-100">
				<i class="ph-caret-right"></i>
			</button>
			<!-- /expand button -->


			<!-- Header -->
			<div class="sidebar-section sidebar-section-body d-flex align-items-center border-bottom">
				<h5 class="mb-0">Sidebar</h5>
				<div class="ms-auto">
					<button type="button" class="btn btn-light border-transparent btn-icon rounded-pill btn-sm sidebar-control sidebar-main-toggle d-none d-lg-inline-flex">
						<i class="ph-arrows-left-right"></i>
					</button>

					<button type="button" class="btn btn-light border-transparent btn-icon rounded-pill btn-sm sidebar-mobile-main-toggle d-lg-none">
						<i class="ph-x"></i>
					</button>
				</div>
			</div>
			<!-- /header -->


			<!-- Sidebar content -->
			<div class="sidebar-content">

				<!-- Sidebar search -->
				<div class="sidebar-section">
					<div class="sidebar-section-header border-bottom">
						<span class="fw-semibold">Sidebar search</span>
						<div class="ms-auto">
	                		<a href="#sidebar-search" class="text-reset" data-bs-toggle="collapse">
	                			<i class="ph-caret-down collapsible-indicator"></i>
	                		</a>
	                	</div>
					</div>

					<div class="collapse show" id="sidebar-search">
						<div class="sidebar-section-body">
							<div class="form-control-feedback form-control-feedback-end">
								<input type="search" class="form-control" placeholder="Search">
								<div class="form-control-feedback-icon">
									<i class="ph-magnifying-glass opacity-50"></i>
								</div>
							</div>
						</div>
					</div>
				</div>
				<!-- /sidebar search -->


				<!-- Actions -->
				<div class="sidebar-section">
					<div class="sidebar-section-header border-bottom">
						<span class="fw-semibold">Actions</span>
						<div class="ms-auto">
	                		<a href="#sidebar-actions" class="text-reset" data-bs-toggle="collapse">
	                			<i class="ph-caret-down collapsible-indicator"></i>
	                		</a>
	                	</div>
					</div>

					<div class="collapse show" id="sidebar-actions">
						<div class="sidebar-section-body">
							<div class="row row-tile g-0">
								<div class="col">
									<button type="button" class="btn btn-light w-100 flex-column rounded-0 rounded-top-start py-2">
										<i class="ph-app-store-logo text-primary ph-2x mb-1"></i>
										App store
									</button>

									<button type="button" class="btn btn-light w-100 flex-column rounded-0 rounded-bottom-start py-2">
										<i class="ph-twitter-logo text-info ph-2x mb-1"></i>
										Twitter
									</button>
								</div>
								
								<div class="col">
									<button type="button" class="btn btn-light w-100 flex-column rounded-0 rounded-top-end py-2">
										<i class="ph-dribbble-logo text-pink ph-2x mb-1"></i>
										Dribbble
									</button>

									<button type="button" class="btn btn-light w-100 flex-column rounded-0 rounded-bottom-end py-2">
										<i class="ph-spotify-logo text-success ph-2x mb-1"></i>
										Spotify
									</button>
								</div>
							</div>
						</div>
					</div>
				</div>
				<!-- /actions -->


				<!-- Sub navigation -->
				<div class="sidebar-section">
					<div class="sidebar-section-header border-bottom">
						<span class="fw-semibold">Navigation</span>
						<div class="ms-auto">
	                		<a href="#sidebar-navigation" class="text-reset" data-bs-toggle="collapse">
								<i class="ph-caret-down collapsible-indicator"></i>
	                		</a>
	                	</div>
					</div>

					<div class="collapse show" id="sidebar-navigation">
						<ul class="nav nav-sidebar mt-2" data-nav-type="accordion">
							<li class="nav-item-header opacity-50">Actions</li>
							<li class="nav-item">
								<a href="#" class="nav-link">
									<i class="ph-plus-circle me-2"></i>
									Create task
								</a>
							</li>
							<li class="nav-item">
								<a href="#" class="nav-link">
									<i class="ph-circles-three-plus me-2"></i>
									Create project
								</a>
							</li>
							<li class="nav-item">
								<a href="#" class="nav-link">
									<i class="ph-pencil me-2"></i>
									Edit task list
								</a>
							</li>
							<li class="nav-item">
								<a href="#" class="nav-link">
									<i class="ph-user-plus me-2"></i>
									Assign users
									<span class="badge bg-primary rounded-pill ms-auto">94 online</span>
								</a>
							</li>
							<li class="nav-item">
								<a href="#" class="nav-link">
									<i class="ph-users-three me-2"></i>
									Create team
								</a>
							</li>
							<li class="nav-item-header opacity-50">Navigate</li>
							<li class="nav-item">
								<a href="#" class="nav-link">
									<i class="ph-kanban me-2"></i>
									All tasks
								</a>
							</li>
							<li class="nav-item">
								<a href="#" class="nav-link">
									<i class="ph-file-plus me-2"></i>
									Active tasks
									<span class="badge bg-dark rounded-pill ms-auto">28</span>
								</a>
							</li>
							<li class="nav-item">
								<a href="#" class="nav-link">
									<i class="ph-file-x me-2"></i>
									Closed tasks
								</a>
							</li>
							<li class="nav-item">
								<a href="#" class="nav-link">
									<i class="ph-user-focus me-2"></i>
									Assigned to me
									<span class="badge bg-info rounded-pill ms-auto">86</span>
								</a>
							</li>
							<li class="nav-item">
								<a href="#" class="nav-link">
									<i class="ph-folder-user me-2"></i>
									Assigned to my team
									<span class="badge bg-danger rounded-pill ms-auto">47</span>
								</a>
							</li>
							<li class="nav-item">
								<a href="#" class="nav-link">
									<i class="ph-gear me-2"></i>
									Settings
								</a>
							</li>
						</ul>
					</div>
				</div>
				<!-- /sub navigation -->


				<!-- Online users -->
				<div class="sidebar-section">
					<div class="sidebar-section-header border-bottom">
						<span class="fw-semibold">Users online</span>
						<div class="ms-auto">
	                		<a href="#sidebar-users" class="text-reset" data-bs-toggle="collapse">
								<i class="ph-caret-down collapsible-indicator"></i>
	                		</a>
	                	</div>
					</div>

					<div class="collapse show" id="sidebar-users">
						<div class="sidebar-section-body">
							<div class="d-flex mb-3">
								<a href="#" class="me-3">
									<img src="../../../assets/images/demo/users/face1.jpg" width="36" height="36" class="rounded-pill" alt="">
								</a>
								<div class="flex-fill">
									<a href="#" class="fw-semibold">James Alexander</a>
									<div class="fs-sm opacity-50">Santa Ana, CA.</div>
								</div>
								<div class="ms-3 align-self-center">
									<div class="bg-success border-success rounded-pill p-1"></div>
								</div>
							</div>

							<div class="d-flex mb-3">
								<a href="#" class="me-3">
									<img src="../../../assets/images/demo/users/face2.jpg" width="36" height="36" class="rounded-pill" alt="">
								</a>
								<div class="flex-fill">
									<a href="#" class="fw-semibold">Jeremy Victorino</a>
									<div class="fs-sm opacity-50">Dowagiac, MI.</div>
								</div>
								<div class="ms-3 align-self-center">
									<div class="bg-danger border-danger rounded-pill p-1"></div>
								</div>
							</div>

							<div class="d-flex mb-3">
								<a href="#" class="me-3">
									<img src="../../../assets/images/demo/users/face3.jpg" width="36" height="36" class="rounded-pill" alt="">
								</a>
								<div class="flex-fill">
									<a href="#" class="fw-semibold">Margo Baker</a>
									<div class="fs-sm opacity-50">Kasaan, AK.</div>
								</div>
								<div class="ms-3 align-self-center">
									<div class="bg-success border-success rounded-pill p-1"></div>
								</div>
							</div>

							<div class="d-flex mb-3">
								<a href="#" class="me-3">
									<img src="../../../assets/images/demo/users/face4.jpg" width="36" height="36" class="rounded-pill" alt="">
								</a>
								<div class="flex-fill">
									<a href="#" class="fw-semibold">Beatrix Diaz</a>
									<div class="fs-sm opacity-50">Neenah, WI.</div>
								</div>
								<div class="ms-3 align-self-center">
									<div class="bg-warning border-warning rounded-pill p-1"></div>
								</div>
							</div>

							<div class="d-flex">
								<a href="#" class="me-3">
									<img src="../../../assets/images/demo/users/face5.jpg" width="36" height="36" class="rounded-pill" alt="">
								</a>
								<div class="flex-fill">
									<a href="#" class="fw-semibold">Richard Vango</a>
									<div class="fs-sm opacity-50">Grapevine, TX.</div>
								</div>
								<div class="ms-3 align-self-center">
									<div class="bg-secondary border-secondary rounded-pill p-1"></div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<!-- /online users -->


				<!-- Filter -->
				<div class="sidebar-section">
					<div class="sidebar-section-header border-bottom">
						<span class="fw-semibold">Filters</span>
						<div class="ms-auto">
	                		<a href="#sidebar-filters" class="text-reset" data-bs-toggle="collapse">
								<i class="ph-caret-down collapsible-indicator"></i>
	                		</a>
	                	</div>
					</div>

					<div class="collapse show" id="sidebar-filters">
						<div class="sidebar-section-body">
							<label class="form-check form-check-reverse text-start mb-2">
								<input type="checkbox" class="form-check-input" checked>
								<span class="form-check-label">Canon</span>
							</label>

							<label class="form-check form-check-reverse text-start mb-2">
								<input type="checkbox" class="form-check-input">
								<span class="form-check-label">Nikon</span>
							</label>

							<label class="form-check form-check-reverse text-start mb-2">
								<input type="checkbox" class="form-check-input" checked>
								<span class="form-check-label">Sony</span>
							</label>

							<label class="form-check form-check-reverse text-start mb-2">
								<input type="checkbox" class="form-check-input" checked>
								<span class="form-check-label">Fuji</span>
							</label>

							<label class="form-check form-check-reverse text-start">
								<input type="checkbox" class="form-check-input">
								<span class="form-check-label">Leica</span>
							</label>
						</div>
					</div>
				</div>
				<!-- /filter -->


				<!-- Latest updates -->
				<div class="sidebar-section">
					<div class="sidebar-section-header border-bottom">
						<span class="fw-semibold">Latest updates</span>
						<div class="ms-auto">
	                		<a href="#sidebar-updates" class="text-reset" data-bs-toggle="collapse">
								<i class="ph-caret-down collapsible-indicator"></i>
	                		</a>
	                	</div>
					</div>

					<div class="collapse show" id="sidebar-updates">
						<div class="sidebar-section-body">
							<div class="d-flex mb-3">
								<div class="me-3">
									<div class="bg-primary bg-opacity-10 text-primary lh-1 rounded-pill p-2">
										<i class="ph-git-pull-request"></i>
									</div>
								</div>

								<div class="flex-fill">
									Drop the IE <a href="#">specific hacks</a> for temporal inputs
									<div class="fs-sm opacity-50">4 minutes ago</div>
								</div>
							</div>

							<div class="d-flex mb-3">
								<div class="me-3">
									<div class="bg-warning bg-opacity-10 text-warning lh-1 rounded-pill p-2">
										<i class="ph-git-commit"></i>
									</div>
								</div>
								
								<div class="flex-fill">
									Add full font overrides for popovers and tooltips
									<div class="fs-sm opacity-50">36 minutes ago</div>
								</div>
							</div>

							<div class="d-flex mb-3">
								<div class="me-3">
									<div class="bg-info bg-opacity-10 text-info lh-1 rounded-pill p-2">
										<i class="ph-git-branch"></i>
									</div>
								</div>
								
								<div class="flex-fill">
									<a href="#">Chris Arney</a> created a new <span class="fw-semibold">Design</span> branch
									<div class="fs-sm opacity-50">2 hours ago</div>
								</div>
							</div>

							<div class="d-flex mb-3">
								<div class="me-3">
									<div class="bg-success bg-opacity-10 text-success lh-1 rounded-pill p-2">
										<i class="ph-git-merge"></i>
									</div>
								</div>
								
								<div class="flex-fill">
									<a href="#">Eugene Kopyov</a> merged <span class="fw-semibold">Master</span> and <span class="fw-semibold">Dev</span> branches
									<div class="fs-sm opacity-50">Dec 18, 18:36</div>
								</div>
							</div>

							<div class="d-flex">
								<div class="me-3">
									<div class="bg-primary bg-opacity-10 text-primary lh-1 rounded-pill p-2">
										<i class="ph-git-pull-request"></i>
									</div>
								</div>
								
								<div class="flex-fill">
									Have Carousel ignore keyboard events
									<div class="fs-sm opacity-50">Dec 12, 05:46</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<!-- /latest updates -->

			</div>
			<!-- /sidebar content -->

		</div>
		<!-- /main sidebar -->


		<!-- Main content -->
		<div class="content-wrapper">

			<!-- Inner content -->
			<div class="content-inner">

				<!-- Page header -->
				<div class="page-header">
					<div class="page-header-content container d-lg-flex">
						<div class="d-flex">
							<h4 class="page-title mb-0">
								Sidebars - <span class="fw-normal">Sticky Header</span>
							</h4>

							<a href="#page_header" class="btn btn-light align-self-center collapsed d-lg-none border-transparent rounded-pill p-0 ms-auto" data-bs-toggle="collapse">
								<i class="ph-caret-down collapsible-indicator ph-sm m-1"></i>
							</a>
						</div>

						<div class="collapse d-lg-block my-lg-auto ms-lg-auto" id="page_header">
							<div class="d-sm-flex align-items-center mb-3 mb-lg-0 ms-lg-3">
								<div class="dropdown w-100 w-sm-auto">
									<a href="#" class="d-flex align-items-center text-body lh-1 dropdown-toggle py-sm-2" data-bs-toggle="dropdown" data-bs-display="static">
										<img src="../../../assets/images/brands/tesla.svg" class="w-32px h-32px me-2" alt="">
										<div class="me-auto me-lg-1">
											<div class="fs-sm text-muted mb-1">Customer</div>
											<div class="fw-semibold">Tesla Motors Inc</div>
										</div>
									</a>

									<div class="dropdown-menu dropdown-menu-lg-end w-100 w-lg-auto wmin-300 wmin-sm-350 pt-0">
										<div class="d-flex align-items-center p-3">
											<h6 class="fw-semibold mb-0">Customers</h6>
											<a href="#" class="ms-auto">
												View all
												<i class="ph-arrow-circle-right ms-1"></i>
											</a>
										</div>
										<a href="#" class="dropdown-item active py-2">
											<img src="../../../assets/images/brands/tesla.svg" class="w-32px h-32px me-2" alt="">
											<div>
												<div class="fw-semibold">Tesla Motors Inc</div>
												<div class="fs-sm text-muted">42 users</div>
											</div>
										</a>
										<a href="#" class="dropdown-item py-2">
											<img src="../../../assets/images/brands/debijenkorf.svg" class="w-32px h-32px me-2" alt="">
											<div>
												<div class="fw-semibold">De Bijenkorf</div>
												<div class="fs-sm text-muted">49 users</div>
											</div>
										</a>
										<a href="#" class="dropdown-item py-2">
											<img src="../../../assets/images/brands/klm.svg" class="w-32px h-32px me-2" alt="">
											<div>
												<div class="fw-semibold">Royal Dutch Airlines</div>
												<div class="fs-sm text-muted">18 users</div>
											</div>
										</a>
										<a href="#" class="dropdown-item py-2">
											<img src="../../../assets/images/brands/shell.svg" class="w-32px h-32px me-2" alt="">
											<div>
												<div class="fw-semibold">Royal Dutch Shell</div>
												<div class="fs-sm text-muted">54 users</div>
											</div>
										</a>
										<a href="#" class="dropdown-item py-2">
											<img src="../../../assets/images/brands/bp.svg" class="w-32px h-32px me-2" alt="">
											<div>
												<div class="fw-semibold">BP plc</div>
												<div class="fs-sm text-muted">23 users</div>
											</div>
										</a>
									</div>
								</div>

								<div class="vr d-none d-sm-block flex-shrink-0 my-2 mx-3"></div>

								<div class="d-inline-flex mt-3 mt-sm-0">
									<a href="#" class="status-indicator-container ms-1">
										<img src="../../../assets/images/demo/users/face24.jpg" class="w-32px h-32px rounded-pill" alt="">
										<span class="status-indicator bg-warning"></span>
									</a>
									<a href="#" class="status-indicator-container ms-1">
										<img src="../../../assets/images/demo/users/face1.jpg" class="w-32px h-32px rounded-pill" alt="">
										<span class="status-indicator bg-success"></span>
									</a>
									<a href="#" class="status-indicator-container ms-1">
										<img src="../../../assets/images/demo/users/face3.jpg" class="w-32px h-32px rounded-pill" alt="">
										<span class="status-indicator bg-danger"></span>
									</a>
									<a href="#" class="btn btn-outline-primary btn-icon w-32px h-32px rounded-pill ms-3">
										<i class="ph-plus"></i>
									</a>
								</div>
							</div>
						</div>
					</div>
				</div>
				<!-- /page header -->


				<!-- Content area -->
				<div class="content container pt-0">

					<!-- Info alert -->
					<div class="alert alert-success alert-dismissible">
						<div class="alert-heading fw-semibold">Sticky sidebar header</div>
						Sidebar header can be sticked to the top of the sidebar content so that it always stays visible regardless scroll position. To achieve that, just move container outside <code>.sidebar-content</code> container. Sidebar on the left demonstrates this option.
						<button type="button" class="btn-close" data-bs-dismiss="alert"></button>
				    </div>
				    <!-- /info alert -->


					<!-- Sidebars overview -->
					<div class="card">
						<div class="card-header">
							<h5 class="mb-0">Sidebars overview</h5>
						</div>

						<div class="card-body">
							<p class="mb-3">Sidebar - vertical area that displays onscreen and presents widget components and website navigation menu in a text-based hierarchical form. All sidebars are css-driven - just add one of css classes to the <code>.sidebar</code> container, and sidebar will change its width and color. No js, css only. Although sidebar type is based on css, buttons do their job with JS - they switch necessary classes in <code>.sidebar</code> container. Below you'll find summarized tables with all available <code>button</code> and <code>sidebar</code> container classes. By default, the template includes 6 different sidebar types and combinations:</p>

							<div class="mb-3">
								<h6>1. Default sidebar</h6>
								<p>Default template sidebar has <code>300px</code> (~18.75rem) width, aligned to the left (to the right in RTL version) and has dark blue background color. All navigation levels are based on accordion <strong>or</strong> collapsible functionality, open on click. Supports 2 versions: fixed (default) and static (in static layout only). Both versions use default browser scrollbars, but support custom scrollbars such as <code>perfect scrollbar</code> component.</p>
							</div>

							<div class="mb-3">
								<h6>2. Mini sidebar</h6>
								<p>Mini sidebar has <code>56px</code> width, which is calculated dynamically (icon size + double padding). No text in parent level of menu items, aligned to the left (to the right in RTL version) and has dark blue background color. Sidebar changes the width on hover, no additional changes. It is <strong>required</strong> to add <code>.sidebar-main-resized</code> class to the <code>.sidebar</code> container if you want to have it collapsed by default. This class is responsible for sidebar width and main navigation. By default all components except main navigation are hidden in mini sidebar. Can be used with main sidebar only.</p>
							</div>

							<div class="mb-3">
								<h6>3. Secondary sidebar</h6>
								<p>Main sidebar has <code>300px</code> width or <code>56px</code> (if <code>.sidebar-main-resized</code> class added). Secondary sidebar has the same fixed width of <code>300px</code>, which is similar to default and right sidebars, so different sidebar components can be placed to all sidebar types. Main and secondary sidebars can contain any content - menu, navigation, buttons, lists, tabs etc. Secondary sidebar can be either collapsed or hidden.</p>
							</div>

							<div class="mb-3">
								<h6>4. Right sidebar</h6>
								<p>Right sidebar layout includes additional sidebar displayed on the right (left in RTL direction) side. It is displayed as an additional component with 100% height, similar to other sidebars. Right sidebar is visible by default, but can be collapsed or hidden.</p>
							</div>

							<div class="mb-3">
								<h6>5. Right/Secondary sidebars</h6>
								<p>Secondary and Right sidebars can be used together, so basically it is a 4 column layout. The width of any sidebar doesn't affect other layout columns, they all have independent width controls. Refer to the table below for more information.</p>
							</div>

							<div class="mb-3">
								<h6>6. Content (component) sidebar</h6>
								<p>Usually sidebar is not a part of content and mainly used for navigation. Limitless allows you to use sidebar outside and inside content area. Content sidebar isn't based on grid and has the same width as other sidebars, this means all sidebar components can be placed inside content sidebar. Supports left and right positioning and can be either stretched to fill all available height or height that depends on sidebar content height.</p>
							</div>
						</div>
					</div>
					<!-- /sidebars overview -->


					<!-- Button classes -->
					<div class="card">
						<div class="card-header">
							<h5 class="mb-0">Button classes</h5>
						</div>

						<div class="card-body">
							<h6>Overview</h6>
							<p class="mb-3">This table displays all optional <code>button</code> classes, responsible for the sidebar appearance. Depending on the sidebar type, add one of these classes to any button or link and this element will handle sidebar control. Multiple controls are also available - add as many sidebar controls as you wish. Please note: these classes don't change sidebar markup, only CSS rules.</p>
							<div class="table-responsive border rounded mb-4">
								<table class="table">
									<thead class="table-light">
										<tr>
											<th class="border-top-0" style="width: 300px;">Button class</th>
											<th class="border-top-0">Action</th>
										</tr>
									</thead>
									<tbody>
										<tr>
											<td><code>.sidebar-main-resize</code></td>
											<td>Resizable sidebar. Changes main sidebar width from default to mini. This button is added to all pages by default.</td>
										</tr>
										<tr>
											<td><code>.sidebar-main-toggle</code></td>
											<td>Collapses/expands and/or hides/shows <code>main</code> sidebar. Used mostly in dual sidebar type to hide main sidebar.</td>
										</tr>
										<tr>
											<td><code>.sidebar-end-toggle</code></td>
											<td>Toggles right sidebar - if right sidebar is shown, main sidebar width remains the same, whether it's in default or mini mode.</td>
										</tr>
										<tr>
											<td><code>.sidebar-secondary-toggle</code></td>
											<td>Hides/shows or collapses/expands <code>secondary</code> sidebar. Secondary sidebar supports only toggle functionality and always has fixed width of <code>300px</code>.</td>
										</tr>
										<tr>
											<td><code>.sidebar-component-toggle</code></td>
											<td>Hides/shows <code>content</code> sidebars. Content sidebars aren't connected with other sidebars, so this is the only button that controls their visibility.</td>
										</tr>
										<tr class="border-top-width-2">
											<td><code>.sidebar-mobile-main-toggle</code></td>
											<td>Toggles <code>main</code> sidebar on mobile - slides from left to right.</td>
										</tr>
										<tr>
											<td><code>.sidebar-mobile-secondary-toggle</code></td>
											<td>Toggles <code>secondary</code> sidebar on mobile - slides from left to right.</td>
										</tr>
										<tr>
											<td><code>.sidebar-mobile-end-toggle</code></td>
											<td>Toggles <code>right</code> sidebar on mobile - slides from right to left.</td>
										</tr>
										<tr>
											<td><code>.sidebar-mobile-component-toggle</code></td>
											<td>Toggles <code>content</code> sidebar on mobile - has full width by default, has no animation.</td>
										</tr>
									</tbody>
								</table>
							</div>

							<h6>Example Markup</h6>
							<p>Default placement of sidebar control buttons is sidebar header:</p>
							<pre class="language-markup mb-3" data-line="13-19">
								<code>
									&lt;!-- Main sidebar -->
									&lt;div class="sidebar sidebar-dark sidebar-main sidebar-expand-lg">

										&lt;!-- Sidebar content -->
										&lt;div class="sidebar-content">

											&lt;!-- Header -->
											&lt;div class="sidebar-section">
												&lt;div class="sidebar-section-body d-flex justify-content-center">
													&lt;h6 class="sidebar-resize-hide flex-grow-1 my-auto">Navigation&lt;/h6>

													&lt;div>
														&lt;button type="button" class="[button classes]">
															&lt;i class="ph-arrows-left-right">&lt;/i>
														&lt;/button>

														&lt;button type="button" class="[button classes]">
															&lt;i class="ph-x">&lt;/i>
														&lt;/button>
													&lt;/div>
												&lt;/div>
											&lt;/div>
											&lt;!-- /header -->

											[other content]

										&lt;/div>
										&lt;!-- /sidebar content -->

									&lt;/div>
									&lt;!-- /main sidebar -->
								</code>
							</pre>

							<p>Here is an example of button inside navbar:</p>
							<pre class="language-markup" data-line="7-9">
								<code>
									&lt;!-- Navbar placement -->
									&lt;div class="navbar navbar-expand navbar-dark">
										&lt;div class="navbar-brand">...&lt;/div>

										&lt;ul class="navbar-nav">
											&lt;li class="nav-item">
												&lt;a href="#" class="navbar-nav-link sidebar-control sidebar-main-toggle">
													&lt;i class="ph-arrows-left-right">&lt;/i>
												&lt;/a>
											&lt;/li>
											...
										&lt;/ul>
									&lt;/div>
									&lt;!-- /navbar placement -->
								</code>
							</pre>
						</div>
					</div>
					<!-- /button classes -->


					<!-- Sidebar classes -->
					<div class="card">
						<div class="card-header">
							<h5 class="mb-0">Sidebar classes</h5>
						</div>

						<div class="card-body">
							<h6>Overview</h6>
							<p class="mb-3">This table demonstrates all classes for <code>sidebar</code> container, responsible for the sidebar width and color. Almost all of these classes are mandatory, some of them are responsible for proper styling or have a specific code attached to this class (like <code>.sidebar-main</code> class, which has collapsible functionality). All classes can be combined depending on the type of sidebar:</p>
							<div class="table-responsive border rounded mb-4">
								<table class="table">
									<thead class="table-light">
										<tr>
											<th class="border-top-0" style="width: 300px">Body class</th>
											<th class="border-top-0">Description</th>
										</tr>
									</thead>
									<tbody>
										<tr>
											<td><code>.sidebar</code></td>
											<td>Default sidebar class, should be added in all layout types.</td>
										</tr>
										<tr>
											<td><code>.sidebar-main</code></td>
											<td>Defines <strong>main</strong> sidebar. Mini sidebar (<code>.sidebar-main-resized</code> class) takes effect only if sidebar has <code>.sidebar-main</code> class. By default, all components except main navigation are hidden in mini sidebar.</td>
										</tr>
										<tr>
											<td><code>.sidebar-main-resized</code></td>
											<td>Defines <strong>main</strong> sidebar in <code>collapsed</code> state</td>
										</tr>
										<tr>
											<td><code>.sidebar-secondary</code></td>
											<td>Defines <strong>secondary</strong> sidebar. Has fixed <code>270px</code> width and usually comes after main sidebar.</td>
										</tr>
										<tr>
											<td><code>.sidebar-secondary-collapsed</code></td>
											<td>Defines <strong>secondary</strong> sidebar in <code>collapsed</code> state</td>
										</tr>
										<tr>
											<td><code>.sidebar-end</code></td>
											<td>Defines <strong>right</strong> sidebar. Has fixed <code>270px</code> width and appears on the right side from the main sidebar.</td>
										</tr>
										<tr>
											<td><code>.sidebar-end-collapsed</code></td>
											<td>Defines <strong>right</strong> sidebar in <code>collapsed</code> state</td>
										</tr>
										<tr>
											<td><code>.sidebar-component</code></td>
											<td>This class is required in <strong>content</strong> (or component) sidebar. Also requires <code>.sidebar-component-left</code> or <code>.sidebar-component-right</code> classes for proper spacing.</td>
										</tr>
										<tr>
											<td><code>.sidebar-component-collapsed</code></td>
											<td>Defines <strong>content</strong> sidebar in collapsed state</td>
										</tr>
										<tr>
											<td><code>.sidebar-dark</code></td>
											<td>Defines <strong>dark</strong> sidebar. This class can be applied to all sidebar types and positions. This class is also required for custom colors (see below).</td>
										</tr>
										<tr>
											<td><code>.sidebar-dark.bg-*</code></td>
											<td>Defines sidebar background color. According to the custom color system, sidebar background color can be changed to one of the available colors by adding a proper class to the main sidebar container.</td>
										</tr>
										<tr>
											<td><code>.sidebar-expand-[breakpoint]</code></td>
											<td>This class specifies when sidebar needs to be collapsed, basically when sidebar switches to mobile mode. Breakpoint should always be similar to <strong>navbar</strong> breakpoint for proper matching. Available breakpoints are: xl, lg, md and sm. This class is required.</td>
										</tr>
										<tr>
											<td><code>.sidebar-main-unfold</code></td>
											<td>This class gets added when user hovers on mini sidebar. It controls resizable behaviour when main sidebar is collapsed. Has no effect on mobile since all sidebars on mobile have same width.</td>
										</tr>
									</tbody>
								</table>
							</div>

							<h6>Example Markup</h6>
							<p>Default left aligned sidebar markup:</p>
							<pre class="language-markup mb-3" data-line="15">
								<code>
									&lt;!-- Default sidebar layout -->
									&lt;body>

										&lt;!-- Navbar -->
										&lt;div class="navbar navbar-dark navbar-expand-lg">
											...
										&lt;/div>
										&lt;!-- /navbar -->


										&lt;!-- Page container -->
										&lt;div class="page-content">

											&lt;!-- Main sidebar -->
											&lt;div class="sidebar sidebar-dark sidebar-main sidebar-expand-lg">
												&lt;div class="sidebar-content">
													...
												&lt;/div>
											&lt;/div>
											&lt;!-- /main sidebar -->

											&lt;!-- Main content -->
											&lt;div class="content-wrapper">
												...
											&lt;/div>
											&lt;!-- /main content -->

										&lt;/div>
										&lt;!-- /page content -->

									&lt;/body>
									&lt;!-- /default sidebar layout -->
								</code>
							</pre>

							<p>Mini sidebar markup. The only difference is <code>.sidebar-main-resized</code> class:</p>
							<pre class="language-markup" data-line="15">
								<code>
									&lt;!-- Mini sidebar layout -->
									&lt;body>

										&lt;!-- Navbar -->
										&lt;div class="navbar navbar-dark navbar-expand-lg">
											...
										&lt;/div>
										&lt;!-- /navbar -->


										&lt;!-- Page container -->
										&lt;div class="page-content">

											&lt;!-- Main sidebar -->
											&lt;div class="sidebar sidebar-dark sidebar-main sidebar-expand-lg sidebar-main-resized">
												&lt;div class="sidebar-content">
													...
												&lt;/div>
											&lt;/div>
											&lt;!-- /main sidebar -->

											&lt;!-- Main content -->
											&lt;div class="content-wrapper">
												...
											&lt;/div>
											&lt;!-- /main content -->

										&lt;/div>
										&lt;!-- /page content -->

									&lt;/body>
									&lt;!-- /mini sidebar layout -->
								</code>
							</pre>
						</div>
					</div>
					<!-- /sidebar classes -->

				</div>
				<!-- /content area -->


				<!-- Footer -->
				<div class="navbar navbar-sm navbar-footer border-top">
					<div class="container-fluid">
						<span>&copy; 2022 <a href="https://themeforest.net/item/limitless-responsive-web-application-kit/13080328">Limitless Web App Kit</a></span>

						<ul class="nav">
							<li class="nav-item">
								<a href="https://kopyov.ticksy.com/" class="navbar-nav-link navbar-nav-link-icon rounded" target="_blank">
									<div class="d-flex align-items-center mx-md-1">
										<i class="ph-lifebuoy"></i>
										<span class="d-none d-md-inline-block ms-2">Support</span>
									</div>
								</a>
							</li>
							<li class="nav-item ms-md-1">
								<a href="https://demo.interface.club/limitless/demo/Documentation/index.html" class="navbar-nav-link navbar-nav-link-icon rounded" target="_blank">
									<div class="d-flex align-items-center mx-md-1">
										<i class="ph-file-text"></i>
										<span class="d-none d-md-inline-block ms-2">Docs</span>
									</div>
								</a>
							</li>
							<li class="nav-item ms-md-1">
								<a href="https://themeforest.net/item/limitless-responsive-web-application-kit/13080328?ref=kopyov" class="navbar-nav-link navbar-nav-link-icon text-primary bg-primary bg-opacity-10 fw-semibold rounded" target="_blank">
									<div class="d-flex align-items-center mx-md-1">
										<i class="ph-shopping-cart"></i>
										<span class="d-none d-md-inline-block ms-2">Purchase</span>
									</div>
								</a>
							</li>
						</ul>
					</div>
				</div>
				<!-- /footer -->

			</div>
			<!-- /inner content -->

		</div>
		<!-- /main content -->

	</div>
	<!-- /page content -->


	<!-- Notifications -->
	<div class="offcanvas offcanvas-end" tabindex="-1" id="notifications">
		<div class="offcanvas-header py-0">
			<h5 class="offcanvas-title py-3">Activity</h5>
			<button type="button" class="btn btn-light btn-sm btn-icon border-transparent rounded-pill" data-bs-dismiss="offcanvas">
				<i class="ph-x"></i>
			</button>
		</div>

		<div class="offcanvas-body p-0">
			<div class="bg-light fw-medium py-2 px-3">New notifications</div>
			<div class="p-3">
				<div class="d-flex align-items-start mb-3">
					<a href="#" class="status-indicator-container me-3">
						<img src="../../../assets/images/demo/users/face1.jpg" class="w-40px h-40px rounded-pill" alt="">
						<span class="status-indicator bg-success"></span>
					</a>
					<div class="flex-fill">
						<a href="#" class="fw-semibold">James</a> has completed the task <a href="#">Submit documents</a> from <a href="#">Onboarding</a> list

						<div class="bg-light rounded p-2 my-2">
							<label class="form-check ms-1">
								<input type="checkbox" class="form-check-input" checked disabled>
								<del class="form-check-label">Submit personal documents</del>
							</label>
						</div>

						<div class="fs-sm text-muted mt-1">2 hours ago</div>
					</div>
				</div>

				<div class="d-flex align-items-start mb-3">
					<a href="#" class="status-indicator-container me-3">
						<img src="../../../assets/images/demo/users/face3.jpg" class="w-40px h-40px rounded-pill" alt="">
						<span class="status-indicator bg-warning"></span>
					</a>
					<div class="flex-fill">
						<a href="#" class="fw-semibold">Margo</a> has added 4 users to <span class="fw-semibold">Customer enablement</span> channel

						<div class="d-flex my-2">
							<a href="#" class="status-indicator-container me-1">
								<img src="../../../assets/images/demo/users/face10.jpg" class="w-32px h-32px rounded-pill" alt="">
								<span class="status-indicator bg-danger"></span>
							</a>
							<a href="#" class="status-indicator-container me-1">
								<img src="../../../assets/images/demo/users/face11.jpg" class="w-32px h-32px rounded-pill" alt="">
								<span class="status-indicator bg-success"></span>
							</a>
							<a href="#" class="status-indicator-container me-1">
								<img src="../../../assets/images/demo/users/face12.jpg" class="w-32px h-32px rounded-pill" alt="">
								<span class="status-indicator bg-success"></span>
							</a>
							<a href="#" class="status-indicator-container me-1">
								<img src="../../../assets/images/demo/users/face13.jpg" class="w-32px h-32px rounded-pill" alt="">
								<span class="status-indicator bg-success"></span>
							</a>
							<button type="button" class="btn btn-light btn-icon d-inline-flex align-items-center justify-content-center w-32px h-32px rounded-pill p-0">
								<i class="ph-plus ph-sm"></i>
							</button>
						</div>

						<div class="fs-sm text-muted mt-1">3 hours ago</div>
					</div>
				</div>

				<div class="d-flex align-items-start">
					<div class="me-3">
						<div class="bg-warning bg-opacity-10 text-warning rounded-pill">
							<i class="ph-warning p-2"></i>
						</div>
					</div>
					<div class="flex-1">
						Subscription <a href="#">#466573</a> from 10.12.2021 has been cancelled. Refund case <a href="#">#4492</a> created
						<div class="fs-sm text-muted mt-1">4 hours ago</div>
					</div>
				</div>
			</div>

			<div class="bg-light fw-medium py-2 px-3">Older notifications</div>
			<div class="p-3">
				<div class="d-flex align-items-start mb-3">
					<a href="#" class="status-indicator-container me-3">
						<img src="../../../assets/images/demo/users/face25.jpg" class="w-40px h-40px rounded-pill" alt="">
						<span class="status-indicator bg-success"></span>
					</a>
					<div class="flex-fill">
						<a href="#" class="fw-semibold">Nick</a> requested your feedback and approval in support request <a href="#">#458</a>

						<div class="my-2">
							<a href="#" class="btn btn-success btn-sm me-1">
								<i class="ph-checks ph-sm me-1"></i>
								Approve
							</a>
							<a href="#" class="btn btn-light btn-sm">
								Review
							</a>
						</div>

						<div class="fs-sm text-muted mt-1">3 days ago</div>
					</div>
				</div>

				<div class="d-flex align-items-start mb-3">
					<a href="#" class="status-indicator-container me-3">
						<img src="../../../assets/images/demo/users/face24.jpg" class="w-40px h-40px rounded-pill" alt="">
						<span class="status-indicator bg-grey"></span>
					</a>
					<div class="flex-fill">
						<a href="#" class="fw-semibold">Mike</a> added 1 new file(s) to <a href="#">Product management</a> project

						<div class="bg-light rounded p-2 my-2">
							<div class="d-flex align-items-center">
								<div class="me-2">
									<img src="../../../assets/images/icons/pdf.svg" width="34" height="34" alt="">
								</div>
								<div class="flex-fill">
									new_contract.pdf
									<div class="fs-sm text-muted">112KB</div>
								</div>
								<div class="ms-2">
									<button type="button" class="btn btn-flat-dark text-body btn-icon btn-sm border-transparent rounded-pill">
										<i class="ph-arrow-down"></i>
									</button>
								</div>
							</div>
						</div>

						<div class="fs-sm text-muted mt-1">1 day ago</div>
					</div>
				</div>

				<div class="d-flex align-items-start mb-3">
					<div class="me-3">
						<div class="bg-success bg-opacity-10 text-success rounded-pill">
							<i class="ph-calendar-plus p-2"></i>
						</div>
					</div>
					<div class="flex-fill">
						All hands meeting will take place coming Thursday at 13:45.

						<div class="my-2">
							<a href="#" class="btn btn-primary btn-sm">
								<i class="ph-calendar-plus ph-sm me-1"></i>
								Add to calendar
							</a>
						</div>

						<div class="fs-sm text-muted mt-1">2 days ago</div>
					</div>
				</div>

				<div class="d-flex align-items-start mb-3">
					<a href="#" class="status-indicator-container me-3">
						<img src="../../../assets/images/demo/users/face4.jpg" class="w-40px h-40px rounded-pill" alt="">
						<span class="status-indicator bg-danger"></span>
					</a>
					<div class="flex-fill">
						<a href="#" class="fw-semibold">Christine</a> commented on your community <a href="#">post</a> from 10.12.2021

						<div class="fs-sm text-muted mt-1">2 days ago</div>
					</div>
				</div>

				<div class="d-flex align-items-start mb-3">
					<div class="me-3">
						<div class="bg-primary bg-opacity-10 text-primary rounded-pill">
							<i class="ph-users-four p-2"></i>
						</div>
					</div>
					<div class="flex-fill">
						<span class="fw-semibold">HR department</span> requested you to complete internal survey by Friday

						<div class="fs-sm text-muted mt-1">3 days ago</div>
					</div>
				</div>

				<div class="text-center">
					<div class="spinner-border" role="status">
						<span class="visually-hidden">Loading...</span>
					</div>
				</div>
			</div>
		</div>
	</div>
	<!-- /notifications -->


	<!-- Demo config -->
	<div class="offcanvas offcanvas-end" tabindex="-1" id="demo_config">
		<div class="position-absolute top-50 end-100 visible">
			<button type="button" class="btn btn-primary btn-icon translate-middle-y rounded-end-0" data-bs-toggle="offcanvas" data-bs-target="#demo_config">
				<i class="ph-gear"></i>
			</button>
		</div>

		<div class="offcanvas-header border-bottom py-0">
			<h5 class="offcanvas-title py-3">Demo configuration</h5>
			<button type="button" class="btn btn-light btn-sm btn-icon border-transparent rounded-pill" data-bs-dismiss="offcanvas">
				<i class="ph-x"></i>
			</button>
		</div>

		<div class="offcanvas-body">
			<div class="fw-semibold mb-2">Color mode</div>
			<div class="list-group mb-3">
				<label class="list-group-item list-group-item-action form-check border-width-1 rounded mb-2">
					<div class="d-flex flex-fill my-1">
						<div class="form-check-label d-flex me-2">
							<i class="ph-sun ph-lg me-3"></i>
							<div>
								<span class="fw-bold">Light theme</span>
								<div class="fs-sm text-muted">Set light theme or reset to default</div>
							</div>
						</div>
						<input type="radio" class="form-check-input cursor-pointer ms-auto" name="main-theme" value="light" checked>
					</div>
				</label>

				<label class="list-group-item list-group-item-action form-check border-width-1 rounded mb-2">
					<div class="d-flex flex-fill my-1">
						<div class="form-check-label d-flex me-2">
							<i class="ph-moon ph-lg me-3"></i>
							<div>
								<span class="fw-bold">Dark theme</span>
								<div class="fs-sm text-muted">Switch to dark theme</div>
							</div>
						</div>
						<input type="radio" class="form-check-input cursor-pointer ms-auto" name="main-theme" value="dark">
					</div>
				</label>

				<label class="list-group-item list-group-item-action form-check border-width-1 rounded mb-0">
					<div class="d-flex flex-fill my-1">
						<div class="form-check-label d-flex me-2">
							<i class="ph-translate ph-lg me-3"></i>
							<div>
								<span class="fw-bold">Auto theme</span>
								<div class="fs-sm text-muted">Set theme based on system mode</div>
							</div>
						</div>
						<input type="radio" class="form-check-input cursor-pointer ms-auto" name="main-theme" value="auto">
					</div>
				</label>
			</div>

			<div class="fw-semibold mb-2">Direction</div>
			<div class="list-group mb-3">
				<label class="list-group-item list-group-item-action form-check border-width-1 rounded mb-0">
					<div class="d-flex flex-fill my-1">
						<div class="form-check-label d-flex me-2">
							<i class="ph-translate ph-lg me-3"></i>
							<div>
								<span class="fw-bold">RTL direction</span>
								<div class="text-muted">Toggle between LTR and RTL</div>
							</div>
						</div>
						<input type="checkbox" name="layout-direction" value="rtl" class="form-check-input cursor-pointer m-0 ms-auto">
					</div>
				</label>
			</div>

			<div class="fw-semibold mb-2">Layouts</div>
			<div class="row">
				<div class="col-12">
					<a href="../../layout_1/full/index.html" class="d-block mb-3">
						<img src="https://demo.interface.club/limitless/assets/images/layouts/layout_1.png" class="img-fluid img-thumbnail" alt="">
					</a>				</div>
				<div class="col-12">
					<a href="../../layout_2/full/index.html" class="d-block mb-3">
						<img src="https://demo.interface.club/limitless/assets/images/layouts/layout_2.png" class="img-fluid img-thumbnail" alt="">
					</a>
				</div>
				<div class="col-12">
					<a href="../../layout_3/full/index.html" class="d-block mb-3">
						<img src="https://demo.interface.club/limitless/assets/images/layouts/layout_3.png" class="img-fluid img-thumbnail" alt="">
					</a>
				</div>
				<div class="col-12">
					<a href="../../layout_4/full/index.html" class="d-block mb-3">
						<img src="https://demo.interface.club/limitless/assets/images/layouts/layout_4.png" class="img-fluid img-thumbnail" alt="">
					</a>
				</div>
				<div class="col-12">
					<a href="../../layout_5/full/index.html" class="d-block mb-3">
						<img src="https://demo.interface.club/limitless/assets/images/layouts/layout_5.png" class="img-fluid img-thumbnail" alt="">
					</a>
				</div>
				<div class="col-12">
					<a href="index.html" class="d-block">
						<img src="https://demo.interface.club/limitless/assets/images/layouts/layout_6.png" class="img-fluid img-thumbnail bg-primary bg-opacity-20 border-primary" alt="">
					</a>
				</div>
			</div>
		</div>

		<div class="border-top text-center py-2 px-3">
			<a href="https://themeforest.net/item/limitless-responsive-web-application-kit/13080328?ref=kopyov" class="btn btn-yellow fw-semibold w-100 my-1" target="_blank">
				<i class="ph-shopping-cart me-2"></i>
				Purchase Limitless
			</a>
		</div>
	</div>
	<!-- /demo config -->

</body>
</html>
