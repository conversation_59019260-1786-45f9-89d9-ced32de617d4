package sics.controller;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.TextStyle;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Properties;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.support.RequestContextUtils;
import sics.cache.CacheInterface;
import sics.domain.AnalystDataWrapper;
import sics.domain.AssetResponse;
import sics.domain.Competition;
import sics.domain.Country;
import sics.domain.Fixture;
import sics.domain.FixtureAnalyst;
import sics.domain.Group;
import sics.domain.Groupset;
import sics.domain.GroupsetChecker;
import sics.domain.LogData;
import sics.domain.ManagerGroupset;
import sics.domain.Order;
import sics.domain.PlayerCompetitions;
import sics.domain.PropertyInputFile;
import sics.domain.ReaperDataWrapper;
import sics.domain.Season;
import sics.domain.Team;
import sics.domain.TeamCompetitions;
import sics.domain.Translation;
import sics.domain.TvUserLimits;
import sics.domain.User;
import sics.domain.UserPlayerReport;
import sics.domain.UserPlayerReportWrapper;
import sics.domain.UserTeamReport;
import sics.domain.UserTeamReportWrapper;
import sics.domain.VtigerAsset;
import sics.domain.VtigerContact;
import sics.domain.VtigerError;
import sics.domain.VtigerField;
import sics.domain.VtigerTask;
import sics.helper.AWSHelper;
import sics.helper.AnalystHelper;
import sics.helper.AssetHelper;
import sics.helper.DateHelper;
import sics.helper.GlobalHelper;
import sics.helper.MailHelper;
import sics.helper.MongoHelper;
import sics.helper.SSHHelper;
import sics.helper.SpringApplicationContextHelper;
import sics.listener.SessionListener;
import sics.service.UserService;

@Controller
@RequestMapping("/user")
public class UserController extends BaseController {

    //inserire le location del mapping qui sotto come stringhe
    private final static String pageAssignGame = "user/assignGame.jsp";
    private final static String pageTrialTv = "user/trialTv.jsp";
    private final static String pageAssignTeamTv = "user/assignTeamTv.jsp";
    private final static String pageAssignPlayerTv = "user/assignPlayerTv.jsp";
    private final static String pageTrialSDA = "user/trialSDA.jsp";
    private final static String pageOrders = "user/orders.jsp";
    private final static String pageVtigerErrors = "user/vtigerErrors.jsp";
    private final static String pageGroupset = "user/groupset.jsp";
    private final static String pageGroupsetChecker = "user/groupsetChecker.jsp";
    private final static String pageLogChecker = "user/logChecker.jsp";
    private final static String pageUserReport = "user/userReport.jsp";
    private final static String pageTranslations = "user/translations.jsp";
    private final static String pageGames = "user/games.jsp";
    private final static String pageGamesReport = "user/gamesReport.jsp";
    private final static String pageGamesReportData = "user/gamesReportData.jsp";
    private final static String pageUserTeamReport = "user/userTeamReport.jsp";
    private final static String pageUserTeamReportDetails = "user/userTeamReportDetails.jsp";
    private final static String pageUserPlayerReport = "user/userPlayerReport.jsp";
    private final static String pageUserPlayerReportDetails = "user/userPlayerReportDetails.jsp";
    private final static String pageReaperReport = "user/reaperReport.jsp";
    private final static String pageReaperReportData = "user/reaperReportData.jsp";

    //riferimento all'oggetto di servizio per interagire con il DAO
    private final UserService mService = new UserService();

    /*
        EXTRA TEMPORARY VARIABLES
     */
    private static final Map<String, List<PropertyInputFile>> temporaryTranslations = new HashMap<>();

    @RequestMapping(value = "/isValidSession")
    public @ResponseBody
    String isValidSession(HttpServletRequest request, HttpServletResponse response, ModelMap model, HttpSession session) {
        try {
            User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
            if (!SessionListener.getSessions().containsKey(curUser.getId()) || !SessionListener.getUserSessions(curUser.getId()).contains(session)) {
                GlobalHelper.writeLogData(session, GlobalHelper.kActionSessionExpired, curUser.getEmail(), curUser.getPassword(), curUser.getId());
                return "expired";
            }
            if (session.getAttribute(GlobalHelper.kLastRequestDate) != null) { // per sicurezza
                Date lastRequest = (Date) session.getAttribute(GlobalHelper.kLastRequestDate);
                int secondsInactivity = session.getMaxInactiveInterval();
                if (DateUtils.addSeconds(lastRequest, secondsInactivity).before(new Date())) {
                    SessionListener.destroySession(session, curUser.getId());
                    return "expired";
                }
            }
            if (SessionListener.getUserMinSessionCreationTime(curUser.getId()) < session.getCreationTime()) {
                session.setMaxInactiveInterval(30); // massimo 30 secondi per dirmi se entrare o uscire
                return "askClearSession";
            }
        } catch (Exception e) {
            GlobalHelper.reportError(e);
            return "expired";
        }
        return "true";
    }

    @RequestMapping(value = "/getIn")
    public @ResponseBody
    String getIn(HttpServletRequest request, HttpServletResponse response, ModelMap model, HttpSession session) {
        try {
            User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
            List<HttpSession> userSessions = new ArrayList<>(SessionListener.getUserSessions(curUser.getId()));
            for (HttpSession tmpSession : userSessions) {
                if (!tmpSession.getId().equals(session.getId())) {
                    SessionListener.destroySession(tmpSession, curUser.getId(), true);
                }
            }
            if (session.getAttribute(GlobalHelper.kMaxSessionDuration) != null) {
                session.setMaxInactiveInterval((int) session.getAttribute(GlobalHelper.kMaxSessionDuration));
            } else {
                GlobalHelper.sendExceptionMail(request, new Exception("Max Session Duration is null on session"));
            }

            GlobalHelper.writeLogData(session, GlobalHelper.kActionSessionUpdated, curUser.getEmail(), curUser.getPassword(), curUser.getId());
        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }
        return "true";
    }

    @RequestMapping(value = "/getOut")
    public @ResponseBody
    String getOut(HttpServletResponse response, ModelMap model, HttpSession session) {
        try {
            User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
            SessionListener.destroySession(session, curUser.getId());
            GlobalHelper.writeLogData(session, GlobalHelper.kActionSessionMaintained, curUser.getEmail(), curUser.getPassword(), curUser.getId());
        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }
        return "true";
    }

    @RequestMapping("/home")
    public String home(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response, @RequestParam(value = "country", required = false) Boolean country) {
        String strLocale = request.getLocale().getLanguage().toLowerCase();
        Locale loc;
        if (strLocale.equalsIgnoreCase("it")) {
            loc = new Locale(strLocale);
        } else {
            loc = new Locale("en");
        }
        RequestContextUtils.getLocaleResolver(request).setLocale(request, response, loc);
        if (!this.initModule(session, model)) {
            return pageRedirect(pageHome);
        }

        return pageHome;
    }

    @RequestMapping("/activeUserData")
    public @ResponseBody
    String activeUserData() {
        List<LogData> activeUsersGroupedByDate = mService.getActiveUsersByWeek();
        StringBuilder activeUsersContent = new StringBuilder("[{name: 'Users Logged', type: 'line', stack: 'Total', smooth: true, symbol: 'circle', symbolSize: 6, data:[");
        for (LogData data : activeUsersGroupedByDate) {
            activeUsersContent.append("[");
            activeUsersContent.append(data.getDate().getTime());
            activeUsersContent.append(",");
            activeUsersContent.append(data.getAmount());
            activeUsersContent.append("],");
        }
        if (!activeUsersGroupedByDate.isEmpty()) {
            activeUsersContent.deleteCharAt(activeUsersContent.length() - 1);
        }
        activeUsersContent.append("]}]");
        return activeUsersContent.toString();
    }

    @RequestMapping("/userActionsData")
    public @ResponseBody
    String userActionsData() {
        List<LogData> userActionsGroupedByMonth = mService.getUserActionsByMonth();
        Map<Date, Map<String, Integer>> groupedData = new LinkedHashMap<>();

        List<String> xValues = new ArrayList<>();
        List<Integer> totalValues = new ArrayList<>();
        List<Integer> loginValues = new ArrayList<>();
        List<Integer> playlistValues = new ArrayList<>();
        List<Integer> watchlistValues = new ArrayList<>();
        List<Integer> downloadValues = new ArrayList<>();
        List<Integer> streamValues = new ArrayList<>();
        List<Integer> xmlJsonValues = new ArrayList<>();

        // raggruppo tutti i dati per mese
        // Map<Mese, Map<Action, Amount>>
        Calendar calendar = Calendar.getInstance();
        for (LogData data : userActionsGroupedByMonth) {
            Date logDate = data.getDate();
            calendar.setTime(logDate);
            calendar.set(Calendar.DAY_OF_MONTH, 1);

            String month = calendar.getDisplayName(Calendar.MONTH, Calendar.LONG, Locale.ENGLISH);
            int year = calendar.get(Calendar.YEAR);
            String xValue = month + " " + year;
            if (!xValues.contains(xValue)) {
                xValues.add(xValue);
            }

            groupedData.putIfAbsent(calendar.getTime(), new HashMap<String, Integer>());
            groupedData.get(calendar.getTime()).put(data.getAction(), data.getAmount());
        }

        for (Date month : groupedData.keySet()) {
            Map<String, Integer> values = groupedData.get(month);
            Integer loginValue = values.get("LOGIN_CORRECT");
            loginValues.add(loginValue == null ? 0 : loginValue);

            Integer playlistValue = values.get("NEW_PLAYLIST_TV");
            playlistValues.add(playlistValue == null ? 0 : playlistValue);

            Integer watchlistValue = values.get("NEW_WATCHLIST");
            watchlistValues.add(watchlistValue == null ? 0 : watchlistValue);

            Integer downloadValue = values.get("VIDEO_DOWNLOAD");
            downloadValues.add(downloadValue == null ? 0 : downloadValue);

            Integer streamValue = values.get("VIDEO_STREAM");
            streamValues.add(streamValue == null ? 0 : streamValue);

            Integer xmlJsonValue = values.get("XML_JSON_DOWNLOAD");
            xmlJsonValues.add(xmlJsonValue == null ? 0 : xmlJsonValue);

            int totalValue = (loginValue == null ? 0 : loginValue)
                    + (playlistValue == null ? 0 : playlistValue)
                    + (watchlistValue == null ? 0 : watchlistValue)
                    + (downloadValue == null ? 0 : downloadValue)
                    + (streamValue == null ? 0 : streamValue)
                    + (xmlJsonValue == null ? 0 : xmlJsonValue);
            totalValues.add(totalValue);
        }

        StringBuilder userActionsContent = new StringBuilder("[");
        userActionsContent.append("{name: 'Totals', type: 'bar', itemStyle: { normal: { barBorderRadius: [4, 4, 0, 0] } }, data: [");
        userActionsContent.append(StringUtils.join(totalValues, ","));
        userActionsContent.append("]},");

        userActionsContent.append("{name: 'Watchlist', type: 'bar', stack: 'Other', data: [");
        userActionsContent.append(StringUtils.join(watchlistValues, ","));
        userActionsContent.append("]},");

        userActionsContent.append("{name: 'Playlists', type: 'bar', stack: 'Other', data: [");
        userActionsContent.append(StringUtils.join(playlistValues, ","));
        userActionsContent.append("]},");

        userActionsContent.append("{name: 'XML/Json', type: 'bar', stack: 'Other', data: [");
        userActionsContent.append(StringUtils.join(xmlJsonValues, ","));
        userActionsContent.append("]},");

        userActionsContent.append("{name: 'Download', type: 'bar', stack: 'Other', data: [");
        userActionsContent.append(StringUtils.join(downloadValues, ","));
        userActionsContent.append("]},");

        userActionsContent.append("{name: 'Stream', type: 'bar', stack: 'Other', data: [");
        userActionsContent.append(StringUtils.join(streamValues, ","));
        userActionsContent.append("]},");

        userActionsContent.append("{name: 'Login', type: 'bar', stack: 'Other', itemStyle: { normal: { barBorderRadius: [4, 4, 0, 0] } }, data: [");
        userActionsContent.append(StringUtils.join(loginValues, ","));
        userActionsContent.append("]},");

        if (!userActionsGroupedByMonth.isEmpty()) {
            userActionsContent.deleteCharAt(userActionsContent.length() - 1);
        }
        userActionsContent.append("]");

        StringBuilder xAxisContent = new StringBuilder("[");
        for (String value : xValues) {
            xAxisContent.append("'");
            xAxisContent.append(value);
            xAxisContent.append("',");
        }
        if (!xValues.isEmpty()) {
            xAxisContent.deleteCharAt(xAxisContent.length() - 1);
        }
        xAxisContent.append("]");

        Gson gson = new Gson();
        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty("actionsContent", userActionsContent.toString());
        jsonObject.addProperty("actionsXValues", xAxisContent.toString());

        return gson.toJson(jsonObject);
    }

    @RequestMapping(value = "/getFixture")
    public @ResponseBody
    String getFixture(HttpServletResponse response, ModelMap model, HttpSession session, HttpServletRequest request,
            @RequestParam("fixtureId") Long fixtureId) {
        if (fixtureId != null) {
            Fixture fixture = mService.getFixture(fixtureId);
            fixture.setNote(mService.getFixtureNote(fixtureId));

            return fixture.getJson();
        }

        return null;
    }

    @RequestMapping("/assignGame")
    public String assignGame(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam(value = "seasonIds", required = false) String seasonIds, @RequestParam(value = "allFixtures", required = false) Boolean allFixtures) {
        String language = "_en";

        if (!this.initModule(session, model)) {
            return pageRedirect(pageHome);
        }
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        if (!curUser.hasAccess(1)) {
            return pageRedirect(pageHome);
        }

        List<Season> seasons = mService.getSeasonsVisible();
        if (seasonIds == null) {
            seasonIds = seasons.get(0).getQueryValue();
        }
//        List<Country> countries = mService.getCountries(seasonIds, language);
//        List<Country> internationalCountries = new ArrayList<>();
//        for (Country country : countries) {
//            if (Long.compare(country.getId(), 0) == 0) { // se internazionale
//                if (country.getInternationalCompetitionId() != null
//                        && Long.compare(country.getInternationalCompetitionId(), 0) != 0) {
//                    internationalCountries.add(country);
//                }
//            }
//        }
//        countries.removeAll(internationalCountries);
//
//        List<Competition> competitions = mService.getCompetitions(seasonIds, language);
        List<Fixture> fixtures = mService.getFixtures(seasonIds, allFixtures, language);
        Map<Long, Country> countriesMap = new HashMap<>();
        Map<Long, Country> internationalCountriesMap = new HashMap<>();
        Map<Long, Competition> competitionsMap = new HashMap<>();
        for (Fixture fixture : fixtures) {
            Long countryId = fixture.getCountryId();
            Long internationalCompetitionId = fixture.getInternationalCompetitionId();
            Long competitionId = fixture.getCompetitionId();
            if (countryId != null && countryId > 0) {
                if (countriesMap.get(countryId) == null) {
                    Country country = new Country();
                    country.setId(countryId);
                    country.setName(fixture.getCountryName());
                    countriesMap.put(countryId, country);
                }
            }
            if (countryId != null && Long.compare(countryId, 0) == 0) {
                if (internationalCompetitionId != null && Long.compare(internationalCompetitionId, 0) != 0) {
                    if (internationalCountriesMap.get(internationalCompetitionId) == null) {
                        Country country = new Country();
                        country.setId(internationalCompetitionId);
                        country.setName(fixture.getInternationalCompetitionName());
                        internationalCountriesMap.put(internationalCompetitionId, country);
                    }
                }
            }
            if (competitionId != null && competitionId > 0) {
                if (competitionsMap.get(competitionId) == null) {
                    Competition competition = new Competition();
                    competition.setId(competitionId);
                    competition.setName(fixture.getCompetitionName());
                    competition.setCountryId(countryId);
                    competition.setInternationalCompetitionId(internationalCompetitionId);
                    competitionsMap.put(competitionId, competition);
                }
            }
        }

        List<Country> countries = new ArrayList<>(countriesMap.values());
        Collections.sort(countries, new Comparator<Country>() {
            @Override
            public int compare(Country o1, Country o2) {
                return o1.getName().compareTo(o2.getName());
            }
        });

        List<Country> internationalCountries = new ArrayList<>(internationalCountriesMap.values());
        Collections.sort(internationalCountries, new Comparator<Country>() {
            @Override
            public int compare(Country o1, Country o2) {
                return o1.getName().compareTo(o2.getName());
            }
        });

        List<Competition> competitions = new ArrayList<>(competitionsMap.values());
        Collections.sort(competitions, new Comparator<Competition>() {
            @Override
            public int compare(Competition o1, Competition o2) {
                return o1.getName().compareTo(o2.getName());
            }
        });

        CacheInterface.updateFixtures(curUser.getId(), fixtures);
        List<User> analysts = mService.getAnalysts();
        List<Group> groups = mService.getGroups();
        Map<Long, Group> groupMap = new HashMap<>();
        for (Group group : groups) {
            groupMap.put(group.getId(), group);
        }

        model.addAttribute("mSeason", seasonIds);
        model.addAttribute("mSeasons", seasons);
        model.addAttribute("mCountries", countries);
        model.addAttribute("mInternationalCountries", internationalCountries);
        model.addAttribute("mCompetitions", competitions);
        model.addAttribute("mAnalysts", analysts);
        model.addAttribute("mGroups", groupMap);

        return pageAssignGame;
    }

    @RequestMapping("/trialTv")
    public String trialTv(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam(value = "ignoreLimit", required = false) Boolean ignoreLimit) {
        if (!this.initModule(session, model)) {
            return pageRedirect(pageHome);
        }
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        if (!curUser.hasAccess(2)) {
            return pageRedirect(pageHome);
        }

        List<VtigerTask> tvTrialRequests = mService.getTvTrialRequests(ignoreLimit);
        model.addAttribute("mRequests", tvTrialRequests);
        // CacheInterface.updateTrialRequests(curUser.getId(), tvTrialRequests);

        return pageTrialTv;
    }

    @RequestMapping("/acceptTrialTv")
    public @ResponseBody
    String acceptTrialTv(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam("activityId") Long activityId, @RequestParam("cmsDateEnd") String cmsDateEnd,
            @RequestParam("ignoreLimit") Boolean ignoreLimit) {
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        if (!curUser.hasAccess(2)) {
            return "permissionDenied";
        }
        String status = "ko";
        // non posso caricare dalla cache altrimenti non gestiamo la concorrenza
        List<VtigerTask> tvTrialRequests = mService.getTvTrialRequests(ignoreLimit);

        if (activityId != null) {
            VtigerTask trial = null;
            for (VtigerTask task : tvTrialRequests) {
                if (task.getId() != null && Long.compare(task.getId(), activityId) == 0) {
                    trial = task;
                    break;
                }
            }

            if (trial != null) {
                if (StringUtils.equalsIgnoreCase(trial.getStatus(), "Planned")) {
                    mService.updateVtigerActivityStatus(activityId, "In Progress");
                    SimpleDateFormat formatter = new SimpleDateFormat("dd/MM/yyyy");
//                    SimpleDateFormat parameterFormatter = new SimpleDateFormat("dd-MM-yyyy");

                    try {
                        Date cmsExpirationDate = formatter.parse(cmsDateEnd);

                        String serialNumber = trial.getSicsTvSN();
                        Long groupsetId = (trial.getVmUserGroupsetId() != null ? trial.getVmUserGroupsetId() : 426L);
                        if (BooleanUtils.isTrue(trial.getHasVideoMatch())) {
                            // se ho videomatch allora creo licenza nuova di sics.tv così da dividere i 2 user
                            if (trial.getVmUserId() != null && StringUtils.isNotBlank(trial.getUserFirstName()) && StringUtils.isNotBlank(trial.getUserLastName())
                                    && StringUtils.isNotBlank(trial.getVmUserGroupsetName()) && trial.getVmUserGroupsetId() != null
                                    && StringUtils.isNotBlank(trial.getUserEmail()) && StringUtils.isNotBlank(trial.getSicsTvSN())
                                    && StringUtils.isNotBlank(trial.getVmUserWebUsername()) && StringUtils.isNotBlank(trial.getVmUserWebPassword())) {
                                Map<String, String> newUserParams = new HashMap<>();
                                newUserParams.put("firstName", trial.getUserFirstName());
                                newUserParams.put("lastName", trial.getUserLastName());
                                newUserParams.put("groupsetName", trial.getVmUserGroupsetName());
                                newUserParams.put("groupsetId", trial.getVmUserGroupsetId().toString());
                                newUserParams.put("email", trial.getUserEmail());
                                newUserParams.put("loginName", "0000");
                                newUserParams.put("snSicsTv", trial.getSicsTvSN());
                                newUserParams.put("webusername", trial.getVmUserWebUsername());
                                newUserParams.put("webpassword", trial.getVmUserWebPassword());
                                Long userId = mService.createTvUser(newUserParams);

                                if (userId != null) {
                                    mService.changeTvUserId(trial.getVmUserId(), userId);
                                    GlobalHelper.writeLogData(session, GlobalHelper.kActionVmTvDivided, curUser.getEmail(), curUser.getPassword(), curUser.getId(), activityId.toString() + "," + trial.getVmUserId() + "," + userId);
                                } else {
                                    return status;
                                }
                            } else {
                                return status;
                            }
                        }

                        AssetResponse assetResponse = AssetHelper.createAsset(trial.getUserToAssign(), 600, serialNumber, groupsetId, cmsExpirationDate, cmsExpirationDate, false, false, false, false, false, false, false, null, null, null);

//                        URL url = new URL("https://server.sics.it/vtigercrm/modules/CMS/actions/ExternalCreation.php");
//                        // Apertura della connessione HTTP
//                        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
//                        // Impostazione del metodo di richiesta come POST
//                        connection.setRequestMethod("POST");
//                        // Attivazione dell'invio di dati
//                        connection.setDoOutput(true);
//                        connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
//                        // Creazione dei parametri da inviare
//                        String parametri = "cms_user_id=" + trial.getUserToAssign();
//                        parametri += "&cms_prod_id=600";
//                        parametri += "&cms_lang=en";
//                        parametri += "&cms_group_id=" + (trial.getVmUserGroupsetId() != null ? (trial.getVmUserGroupsetId() + "_" + trial.getVmUserGroupsetName()) : "426_Ospiti Trial");
//                        if (BooleanUtils.isTrue(trial.getHasVideoMatch())) {
//                            // se ho videomatch allora creo licenza nuova di sics.tv così da dividere i 2 user
//                            if (trial.getVmUserId() != null && StringUtils.isNotBlank(trial.getUserFirstName()) && StringUtils.isNotBlank(trial.getUserLastName())
//                                    && StringUtils.isNotBlank(trial.getVmUserGroupsetName()) && trial.getVmUserGroupsetId() != null
//                                    && StringUtils.isNotBlank(trial.getUserEmail()) && StringUtils.isNotBlank(trial.getSicsTvSN())
//                                    && StringUtils.isNotBlank(trial.getVmUserWebUsername()) && StringUtils.isNotBlank(trial.getVmUserWebPassword())) {
//                                Map<String, String> newUserParams = new HashMap<>();
//                                newUserParams.put("firstName", trial.getUserFirstName());
//                                newUserParams.put("lastName", trial.getUserLastName());
//                                newUserParams.put("groupsetName", trial.getVmUserGroupsetName());
//                                newUserParams.put("groupsetId", trial.getVmUserGroupsetId().toString());
//                                newUserParams.put("email", trial.getUserEmail());
//                                newUserParams.put("snSicsTv", trial.getSicsTvSN());
//                                newUserParams.put("webusername", trial.getVmUserWebUsername());
//                                newUserParams.put("webpassword", trial.getVmUserWebPassword());
//                                Long userId = mService.createTvUser(newUserParams);
//
//                                if (userId != null) {
//                                    mService.changeTvUserId(trial.getVmUserId(), userId);
//                                    parametri += "&cms_vmuser_id=600_" + userId + "_" + trial.getSicsTvSN();
//                                    GlobalHelper.writeLogData(session, GlobalHelper.kActionVmTvDivided, curUser.getEmail(), curUser.getPassword(), curUser.getId(), activityId.toString() + "," + trial.getVmUserId() + "," + userId);
//                                } else {
//                                    return status;
//                                }
//                            } else {
//                                return status;
//                            }
//                        } else {
//                            parametri += "&cms_vmuser_id=" + (trial.getVmUserId() != null ? "600_" + trial.getVmUserId() + "_" + trial.getSicsTvSN() : "");
//                        }
//                        parametri += "&cms_dateend=" + parameterFormatter.format(cmsExpirationDate);        // formato: dd-MM-yyyy
//                        parametri += "&cms_dateend_video=" + parameterFormatter.format(cmsExpirationDate);  // formato: dd-MM-yyyy
//                        parametri += "&cms_tr_camere=0";
//                        parametri += "&cms_email_vm=" + trial.getUserEmail();
//                        parametri += "&cms_pduser_id=";
//                        // Codifica dei parametri in formato URL-encoded
////                parametri = URLEncoder.encode(parametri, "UTF-8");
//                        try (OutputStream os = connection.getOutputStream(); BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(os, "UTF-8"))) {
//                            // Scrittura dei parametri nell'output stream
//                            writer.write(parametri);
//                            writer.flush();
//                        }
//
//                        StringBuilder requestResponse;
//                        try (BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()))) {
//                            String inputLine;
//                            requestResponse = new StringBuilder();
//                            while ((inputLine = in.readLine()) != null) {
//                                requestResponse.append(inputLine);
//                            }
//                        }
//
//                        status = requestResponse.toString();
//                        connection.disconnect();
                        if (assetResponse.getAssetId() != null) {
                            status = "Done";
                            AssetHelper.sendAssetMail(assetResponse.getAssetId());
                        } else {
                            status = StringUtils.defaultIfEmpty(assetResponse.getMessage(), "ko");
                        }

                        if (status.equalsIgnoreCase("Done")) {
                            List<VtigerAsset> assets = mService.getVtigerAssetsByEmail(trial.getUserEmail());
                            if (assets != null && !assets.isEmpty()) {
                                mService.updateVtigerAssetOwnerId(assets.get(0).getAssetId(), trial.getAgentId());
                            }

                            mService.updateVtigerActivityStatus(activityId, "Completed");
                            mService.updateVtigerActivityExpirationDate(activityId, cmsExpirationDate);
                            GlobalHelper.writeLogData(session, GlobalHelper.kActionConfirmTrialTv, curUser.getEmail(), curUser.getPassword(), curUser.getId(), activityId.toString());

                            // mando mail per dire all'agente che è stato approvato
                            try {
                                if (StringUtils.isNotEmpty(trial.getAgentFirstName()) && StringUtils.isNotEmpty(trial.getAgentLastName())
                                        && StringUtils.isNotEmpty(trial.getAgentEmail())
                                        && StringUtils.isNotEmpty(trial.getUserFirstName()) && StringUtils.isNotEmpty(trial.getUserLastName())) {
                                    String to = trial.getAgentEmail();
                                    String subject, body;

                                    body = "Dear <b>" + trial.getAgentFirstName() + " " + trial.getAgentLastName() + "</b>,<br><br>\n"
                                            + "The Sics.tv Trial Request for <b>" + trial.getUserFirstName() + " " + trial.getUserLastName() + "</b> has been <b>ACCEPTED</b> from " + curUser.getFirstName() + " " + curUser.getLastName() + ".<br>\n"
                                            + "Trial will expire on <b>" + cmsDateEnd + "</b>";

                                    subject = "[SICS.TV TRIAL REQUEST] Accepted";

                                    MailHelper mail = new MailHelper(SpringApplicationContextHelper.getMessage("email.smtp"), SpringApplicationContextHelper.getMessage("email.smtp.port"), SpringApplicationContextHelper.getMessage("email.smtp.user"), SpringApplicationContextHelper.getMessage("email.smtp.pwd"));
                                    mail.sendMail(SpringApplicationContextHelper.getMessage("email.from"), to, null, "<EMAIL>", null, null, subject, body, null, "");
                                }
                            } catch (Exception ex) {
                                GlobalHelper.reportError(ex);
                            }

                            if (BooleanUtils.isTrue(trial.getHasVideoMatch())) {
                                status += "|VideoMatch";
                            }
                        }

                        // se è anche richiesta match studio, creo clone del task per mostrarlo nella tab "Match Report"
                        if (BooleanUtils.isTrue(trial.getIsMatchReport()) && trial.getTeamId() != null) {
                            if (trial.getVmUserId() == null) {
                                // potrebbe essere una nuova licenza, devo quindi ricaricare i dati
                                tvTrialRequests = mService.getTvTrialRequests(ignoreLimit);
                                trial = null;
                                for (VtigerTask task : tvTrialRequests) {
                                    if (task.getId() != null && Long.compare(task.getId(), activityId) == 0) {
                                        trial = task;
                                        break;
                                    }
                                }
                            }
                            if (trial != null && trial.getVmUserId() != null) {
//                            Long userTeamId = mService.getUserTeamReportId(trial.getVmUserId(), trial.getTeamId());

                                // creo solo se non è già stato fatto, altrimenti potrei creare doppioni a manetta
//                            if (userTeamId == null) {
                                URL url = new URL("https://server.sics.it/vtigercrm/modules/CMS/actions/APICreation.php");
                                HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                                connection.setRequestMethod("POST");
                                connection.setDoOutput(true);
                                connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
                                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
                                SimpleDateFormat formatTime = new SimpleDateFormat("HH:mm:ss");

                                String parametri = "subject=Match Report " + (StringUtils.isNotBlank(trial.getTeamName()) ? trial.getTeamName() : "");
                                parametri += "&assigneduserid=" + trial.getAgentId();
                                parametri += "&datestart=" + format.format(new Date());
                                parametri += "&timestart=" + formatTime.format(new Date());
                                parametri += "&duedate=" + format.format(new Date());
                                parametri += "&contactId=" + trial.getUserToAssign();
                                parametri += "&taskstatus=Planned";
                                parametri += "&taskpriority=Medium";
                                parametri += "&activitytype=Task";
                                parametri += "&visibility=Private";
                                parametri += "&description=Task created automatically from sicsmanager";
                                parametri += "&source=CRM";
                                parametri += "&cf_1008=1";
                                parametri += "&cf_1010=" + trial.getTeamId();
                                parametri += "&cf_1016=" + format.format(trial.getExpirationDate());
                                parametri += "&label=Match Report " + (StringUtils.isNotBlank(trial.getTeamName()) ? trial.getTeamName() : "");
                                parametri += "&module=Calendar";
                                parametri += "&action=insert";
                                try (OutputStream os = connection.getOutputStream(); BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(os, "UTF-8"))) {
                                    writer.write(parametri);
                                    writer.flush();
                                }

                                StringBuilder requestResponse = null;
                                try (BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()))) {
                                    String inputLine;
                                    requestResponse = new StringBuilder();
                                    while ((inputLine = in.readLine()) != null) {
                                        requestResponse.append(inputLine);
                                    }
                                }

                                String matchReportStatus = requestResponse.toString();
                                connection.disconnect();
//                            }
                            }
                        }

                        // se è anche richiesta player match studio, creo clone del task per mostrarlo nella tab "Match Report"
                        if (BooleanUtils.isTrue(trial.getIsPlayerMatchReport()) && trial.getPlayerId() != null) {
                            if (trial.getVmUserId() == null) {
                                // potrebbe essere una nuova licenza, devo quindi ricaricare i dati
                                tvTrialRequests = mService.getTvTrialRequests(ignoreLimit);
                                trial = null;
                                for (VtigerTask task : tvTrialRequests) {
                                    if (task.getId() != null && Long.compare(task.getId(), activityId) == 0) {
                                        trial = task;
                                        break;
                                    }
                                }
                            }
                            if (trial != null && trial.getVmUserId() != null) {
//                            Long userTeamId = mService.getUserTeamReportId(trial.getVmUserId(), trial.getTeamId());

                                // creo solo se non è già stato fatto, altrimenti potrei creare doppioni a manetta
//                            if (userTeamId == null) {
                                URL url = new URL("https://server.sics.it/vtigercrm/modules/CMS/actions/APICreation.php");
                                HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                                connection.setRequestMethod("POST");
                                connection.setDoOutput(true);
                                connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
                                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
                                SimpleDateFormat formatTime = new SimpleDateFormat("HH:mm:ss");

                                String parametri = "subject=Player Match Report " + (StringUtils.isNotBlank(trial.getPlayerName()) ? trial.getPlayerName() : "");
                                parametri += "&assigneduserid=" + trial.getAgentId();
                                parametri += "&datestart=" + format.format(new Date());
                                parametri += "&timestart=" + formatTime.format(new Date());
                                parametri += "&duedate=" + format.format(new Date());
                                parametri += "&contactId=" + trial.getUserToAssign();
                                parametri += "&taskstatus=Planned";
                                parametri += "&taskpriority=Medium";
                                parametri += "&activitytype=Task";
                                parametri += "&visibility=Private";
                                parametri += "&description=Task created automatically from sicsmanager";
                                parametri += "&source=CRM";
                                parametri += "&cf_1026=1";
                                parametri += "&cf_1030=" + format.format(trial.getExpirationDate());
                                parametri += "&label=Player Match Report " + (StringUtils.isNotBlank(trial.getPlayerName()) ? trial.getPlayerName() : "");
                                parametri += "&module=Calendar";
                                parametri += "&action=insert";
                                try (OutputStream os = connection.getOutputStream(); BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(os, "UTF-8"))) {
                                    writer.write(parametri);
                                    writer.flush();
                                }

                                StringBuilder requestResponse = null;
                                try (BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()))) {
                                    String inputLine;
                                    requestResponse = new StringBuilder();
                                    while ((inputLine = in.readLine()) != null) {
                                        requestResponse.append(inputLine);
                                    }
                                }

                                String matchReportStatus = requestResponse.toString();
                                connection.disconnect();
//                            }
                            }
                        }
                    } catch (IOException | ParseException ex) {
                        GlobalHelper.reportError(ex);
                    }
                } else {
                    if (StringUtils.isNotBlank(trial.getStatus())) {
                        status = trial.getStatus();
                    }
                }
            }
        }

        return status;
    }

    @RequestMapping("/denyTrialTv")
    public @ResponseBody
    String denyTrialTv(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam("activityId") Long activityId, @RequestParam("agent") String agent,
            @RequestParam("agentEmail") String agentEmail, @RequestParam("customer") String customer) {
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        if (!curUser.hasAccess(2)) {
            return "permissionDenied";
        }
        String status = "ko";

        if (activityId != null) {
            mService.updateVtigerActivityStatus(activityId, "Not Accepted");
            GlobalHelper.writeLogData(session, GlobalHelper.kActionDeclineTrialTv, curUser.getEmail(), curUser.getPassword(), curUser.getId(), activityId.toString());
            status = "Done";

            // mando mail per dire all'agente che non è stato approvato
            try {
                if (StringUtils.isNotEmpty(agent) && StringUtils.isNotEmpty(agentEmail) && StringUtils.isNotEmpty(customer)) {
                    String to = agentEmail;
                    String subject, body;

                    body = "Dear <b>" + agent + "</b>,<br><br>\n"
                            + "The Sics.tv Trial Request for <b>" + customer + "</b> has been <b>DECLINED</b> from " + curUser.getFirstName() + " " + curUser.getLastName() + ".";

                    subject = "[SICS.TV TRIAL REQUEST] Not Accepted";

                    MailHelper mail = new MailHelper(SpringApplicationContextHelper.getMessage("email.smtp"), SpringApplicationContextHelper.getMessage("email.smtp.port"), SpringApplicationContextHelper.getMessage("email.smtp.user"), SpringApplicationContextHelper.getMessage("email.smtp.pwd"));
                    mail.sendMail(SpringApplicationContextHelper.getMessage("email.from"), to, null, "<EMAIL>", null, null, subject, body, null, "");
                }
            } catch (Exception ex) {
                GlobalHelper.reportError(ex);
            }
        }

        return status;
    }

    @RequestMapping("/assignTeamTv")
    public String assignTeamTv(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam(value = "ignoreLimit", required = false) Boolean ignoreLimit) {
        if (!this.initModule(session, model)) {
            return pageRedirect(pageHome);
        }
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        if (!curUser.hasAccess(3)) {
            return pageRedirect(pageHome);
        }

        List<VtigerTask> assignTeamRequests = mService.getTvAssignTeamRequests(ignoreLimit);
        model.addAttribute("mRequests", assignTeamRequests);
        // CacheInterface.updateTrialRequests(curUser.getId(), tvTrialRequests);

        return pageAssignTeamTv;
    }

    @RequestMapping("/acceptTeamRequestTv")
    public @ResponseBody
    String acceptTeamRequestTv(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam("activityId") Long activityId, @RequestParam("cmsDateEnd") String cmsDateEnd) {
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        if (!curUser.hasAccess(3)) {
            return "permissionDenied";
        }
        String status = "ko";
        // non posso caricare dalla cache altrimenti non gestiamo la concorrenza
        List<VtigerTask> tvTrialRequests = mService.getTvAssignTeamRequests(null);

        if (activityId != null) {
            VtigerTask teamReqest = null;
            for (VtigerTask task : tvTrialRequests) {
                if (task.getId() != null && Long.compare(task.getId(), activityId) == 0) {
                    teamReqest = task;
                    break;
                }
            }

            if (teamReqest != null) {
                if (StringUtils.equalsIgnoreCase(teamReqest.getStatus(), "Planned")) {
                    mService.updateVtigerActivityStatus(activityId, "In Progress");

                    SimpleDateFormat formatter = new SimpleDateFormat("dd/MM/yyyy");
//                    SimpleDateFormat parameterFormatter = new SimpleDateFormat("yyyy-MM-dd");
                    try {
                        Date cmsExpirationDate = formatter.parse(cmsDateEnd);

//                        Long progressivo = mService.getPdataProgressivo();
//                        if (progressivo != null) {
//                            String serialNumber = GlobalHelper.generateRandomString(6) + progressivo + GlobalHelper.generateRandomString(6) + "601";
//                            String assetName = "601-" + serialNumber;
//                            String action = teamReqest.getTeamReportAssetId() != null ? "update" : "insert";
//                            URL url = new URL("https://server.sics.it/vtigercrm/modules/CMS/actions/APICreation.php");
//                            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
//                            connection.setRequestMethod("POST");
//                            connection.setDoOutput(true);
//                            connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
//
//                            String parametri = "productid=37694";
//                            parametri += "&serialnumber=" + serialNumber;
//                            parametri += "&datesold=" + parameterFormatter.format(new Date());
//                            parametri += "&dateinservice=" + parameterFormatter.format(new Date());
//                            parametri += "&assetstatus=In Service";
//                            parametri += "&assigneduserid=" + teamReqest.getAgentId();
//                            parametri += "&assetname=" + assetName;
//                            parametri += "&expirationdate=" + parameterFormatter.format(cmsExpirationDate);
//                            parametri += "&teamreport=" + teamReqest.getTeamName();
//                            parametri += "&teamreportid=" + teamReqest.getTeamId();
//                            parametri += "&assetId=" + (teamReqest.getTeamReportAssetId() != null ? teamReqest.getTeamReportAssetId() : 0);
//                            parametri += "&contactId=" + teamReqest.getUserToAssign();
//                            parametri += "&module=Assets";
//                            parametri += "&action=" + action;
//                            try (OutputStream os = connection.getOutputStream(); BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(os, "UTF-8"))) {
//                                writer.write(parametri);
//                                writer.flush();
//                            }
//
//                            StringBuilder requestResponse;
//                            try (BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()))) {
//                                String inputLine;
//                                requestResponse = new StringBuilder();
//                                while ((inputLine = in.readLine()) != null) {
//                                    requestResponse.append(inputLine);
//                                }
//                            }
//
//                            status = requestResponse.toString();
//                            connection.disconnect();
                        if (BooleanUtils.isTrue(teamReqest.getTeamReportExists())) {
                            if (teamReqest.getTeamReportAssetId() != null && cmsExpirationDate != null) {
                                mService.updateAssetExpiration(teamReqest.getTeamReportAssetId(), cmsExpirationDate);
                                status = "Done";
                            }
                        } else {
                            AssetResponse assetResponse = AssetHelper.createAsset(teamReqest.getUserToAssign(), 601, null, 2L, cmsExpirationDate, cmsExpirationDate, false, false, false, false, false, false, false, teamReqest.getVmUserId(), teamReqest.getTeamId(), null);
                            if (assetResponse.getAssetId() != null) {
                                status = "Done";
                                // AssetHelper.sendAssetMail(assetResponse.getAssetId());
                            } else {
                                status = StringUtils.defaultIfEmpty(assetResponse.getMessage(), "ko");
                            }
                        }

                        if (status.contains("Done")) {
//                            String responseAssetId = StringUtils.replace(status, "Done", "");
//                            if (StringUtils.isNotBlank(responseAssetId)) {
//                                Long assetId = Long.valueOf(StringUtils.replace(responseAssetId, "29x", ""));
//                                if (BooleanUtils.isFalse(teamReqest.getTeamReportExists())) {
//                                    mService.addUserTeam(teamReqest.getVmUserId(), teamReqest.getTeamId(), teamReqest.getUserToAssign(), assetId);
//                                }
//
//                                mService.updateVtigerActivityStatus(activityId, "Completed");
//                                GlobalHelper.writeLogData(session, GlobalHelper.kActionConfirmTeamRequestTv, curUser.getEmail(), curUser.getPassword(), curUser.getId(), activityId.toString());
//
//                                // mando mail per dire all'agente che è stato approvato
//                                try {
//                                    if (StringUtils.isNotEmpty(teamReqest.getAgentFirstName()) && StringUtils.isNotEmpty(teamReqest.getAgentLastName())
//                                            && StringUtils.isNotEmpty(teamReqest.getAgentEmail())
//                                            && StringUtils.isNotEmpty(teamReqest.getUserFirstName()) && StringUtils.isNotEmpty(teamReqest.getUserLastName())) {
//                                        String to = teamReqest.getAgentEmail();
//                                        String subject, body;
//
//                                        body = "Dear <b>" + teamReqest.getAgentFirstName() + " " + teamReqest.getAgentLastName() + "</b>,<br><br>\n"
//                                                + "The Match Report Request for <b>" + teamReqest.getUserFirstName() + " " + teamReqest.getUserLastName() + "</b> has been <b>ACCEPTED</b> from " + curUser.getFirstName() + " " + curUser.getLastName() + ".\n";
//
//                                        subject = "[SICS.TV TEAM REQUEST] Accepted";
//
//                                        MailHelper mail = new MailHelper(SpringApplicationContextHelper.getMessage("email.smtp"), SpringApplicationContextHelper.getMessage("email.smtp.port"), SpringApplicationContextHelper.getMessage("email.smtp.user"), SpringApplicationContextHelper.getMessage("email.smtp.pwd"));
//                                        mail.sendMail(SpringApplicationContextHelper.getMessage("email.from"), to, null, "<EMAIL>", null, null, subject, body, null, "");
//                                    }
//                                } catch (Exception ex) {
//                                    GlobalHelper.reportError(ex);
//                                }
//                            }
                            mService.updateVtigerActivityStatus(activityId, "Completed");
                            GlobalHelper.writeLogData(session, GlobalHelper.kActionConfirmTeamRequestTv, curUser.getEmail(), curUser.getPassword(), curUser.getId(), activityId.toString());

                            // mando mail per dire all'agente che è stato approvato
                            try {
                                if (StringUtils.isNotEmpty(teamReqest.getAgentFirstName()) && StringUtils.isNotEmpty(teamReqest.getAgentLastName())
                                        && StringUtils.isNotEmpty(teamReqest.getAgentEmail())
                                        && StringUtils.isNotEmpty(teamReqest.getUserFirstName()) && StringUtils.isNotEmpty(teamReqest.getUserLastName())) {
                                    String to = teamReqest.getAgentEmail();
                                    String subject, body;

                                    body = "Dear <b>" + teamReqest.getAgentFirstName() + " " + teamReqest.getAgentLastName() + "</b>,<br><br>\n"
                                            + "The Match Report Request for <b>" + teamReqest.getUserFirstName() + " " + teamReqest.getUserLastName() + "</b> has been <b>ACCEPTED</b> from " + curUser.getFirstName() + " " + curUser.getLastName() + ".\n";

                                    subject = "[SICS.TV TEAM REQUEST] Accepted";

                                    MailHelper mail = new MailHelper(SpringApplicationContextHelper.getMessage("email.smtp"), SpringApplicationContextHelper.getMessage("email.smtp.port"), SpringApplicationContextHelper.getMessage("email.smtp.user"), SpringApplicationContextHelper.getMessage("email.smtp.pwd"));
                                    mail.sendMail(SpringApplicationContextHelper.getMessage("email.from"), to, null, "<EMAIL>", null, null, subject, body, null, "");
                                }
                            } catch (Exception ex) {
                                GlobalHelper.reportError(ex);
                            }
                        }
//                        }
                    } catch (ParseException ex) {
                        GlobalHelper.reportError(ex);
                    }
                } else {
                    if (StringUtils.isNotBlank(teamReqest.getStatus())) {
                        status = teamReqest.getStatus();
                    }
                }
            }
        }

        return status;
    }

    @RequestMapping("/denyTeamRequestTv")
    public @ResponseBody
    String denyTeamRequestTv(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam("activityId") Long activityId, @RequestParam("agent") String agent,
            @RequestParam("agentEmail") String agentEmail, @RequestParam("customer") String customer) {
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        if (!curUser.hasAccess(3)) {
            return "permissionDenied";
        }
        String status = "ko";

        if (activityId != null) {
            mService.updateVtigerActivityStatus(activityId, "Not Accepted");
            GlobalHelper.writeLogData(session, GlobalHelper.kActionDeclineTeamRequestTv, curUser.getEmail(), curUser.getPassword(), curUser.getId(), activityId.toString());
            status = "Done";

            // mando mail per dire all'agente che non è stato approvato
            try {
                if (StringUtils.isNotEmpty(agent) && StringUtils.isNotEmpty(agentEmail) && StringUtils.isNotEmpty(customer)) {
                    String to = agentEmail;
                    String subject, body;

                    body = "Dear <b>" + agent + "</b>,<br><br>\n"
                            + "The Match Report Request for <b>" + customer + "</b> has been <b>DECLINED</b> from " + curUser.getFirstName() + " " + curUser.getLastName() + ".";

                    subject = "[SICS.TV TEAM REQUEST] Not Accepted";

                    MailHelper mail = new MailHelper(SpringApplicationContextHelper.getMessage("email.smtp"), SpringApplicationContextHelper.getMessage("email.smtp.port"), SpringApplicationContextHelper.getMessage("email.smtp.user"), SpringApplicationContextHelper.getMessage("email.smtp.pwd"));
                    mail.sendMail(SpringApplicationContextHelper.getMessage("email.from"), to, null, "<EMAIL>", null, null, subject, body, null, "");
                }
            } catch (Exception ex) {
                GlobalHelper.reportError(ex);
            }
        }

        return status;
    }

    @RequestMapping("/assignPlayerTv")
    public String assignPlayerTv(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam(value = "ignoreLimit", required = false) Boolean ignoreLimit) {
        if (!this.initModule(session, model)) {
            return pageRedirect(pageHome);
        }
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        if (!curUser.hasAccess(3)) {
            return pageRedirect(pageHome);
        }

        List<VtigerTask> assignPlayerRequests = mService.getTvAssignPlayerRequests(ignoreLimit);
        model.addAttribute("mRequests", assignPlayerRequests);
        // CacheInterface.updateTrialRequests(curUser.getId(), tvTrialRequests);

        return pageAssignPlayerTv;
    }

    @RequestMapping("/acceptPlayerRequestTv")
    public @ResponseBody
    String acceptPlayerRequestTv(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam("activityId") Long activityId, @RequestParam("cmsDateEnd") String cmsDateEnd) {
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        if (!curUser.hasAccess(3)) {
            return "permissionDenied";
        }
        String status = "ko";
        // non posso caricare dalla cache altrimenti non gestiamo la concorrenza
        List<VtigerTask> tvTrialRequests = mService.getTvAssignPlayerRequests(null);

        if (activityId != null) {
            VtigerTask playerReqest = null;
            for (VtigerTask task : tvTrialRequests) {
                if (task.getId() != null && Long.compare(task.getId(), activityId) == 0) {
                    playerReqest = task;
                    break;
                }
            }

            if (playerReqest != null) {
                if (StringUtils.equalsIgnoreCase(playerReqest.getStatus(), "Planned")) {
                    mService.updateVtigerActivityStatus(activityId, "In Progress");

                    SimpleDateFormat formatter = new SimpleDateFormat("dd/MM/yyyy");
                    try {
                        Date cmsExpirationDate = formatter.parse(cmsDateEnd);

                        if (BooleanUtils.isTrue(playerReqest.getPlayerReportExists())) {
                            if (playerReqest.getPlayerReportAssetId() != null && cmsExpirationDate != null) {
                                mService.updateAssetExpiration(playerReqest.getPlayerReportAssetId(), cmsExpirationDate);
                                status = "Done";
                            }
                        } else {
                            AssetResponse assetResponse = AssetHelper.createAsset(playerReqest.getUserToAssign(), 602, null, 2L, cmsExpirationDate, cmsExpirationDate, false, false, false, false, false, false, false, playerReqest.getVmUserId(), null, playerReqest.getPlayerId());
                            if (assetResponse.getAssetId() != null) {
                                status = "Done";
                                // AssetHelper.sendAssetMail(assetResponse.getAssetId());
                            } else {
                                status = StringUtils.defaultIfEmpty(assetResponse.getMessage(), "ko");
                            }
                        }

                        if (status.contains("Done")) {
                            mService.updateVtigerActivityStatus(activityId, "Completed");
                            GlobalHelper.writeLogData(session, GlobalHelper.kActionConfirmPlayerRequestTv, curUser.getEmail(), curUser.getPassword(), curUser.getId(), activityId.toString());

                            // mando mail per dire all'agente che è stato approvato
                            try {
                                if (StringUtils.isNotEmpty(playerReqest.getAgentFirstName()) && StringUtils.isNotEmpty(playerReqest.getAgentLastName())
                                        && StringUtils.isNotEmpty(playerReqest.getAgentEmail())
                                        && StringUtils.isNotEmpty(playerReqest.getUserFirstName()) && StringUtils.isNotEmpty(playerReqest.getUserLastName())) {
                                    String to = playerReqest.getAgentEmail();
                                    String subject, body;

                                    body = "Dear <b>" + playerReqest.getAgentFirstName() + " " + playerReqest.getAgentLastName() + "</b>,<br><br>\n"
                                            + "The Match Report Request for <b>" + playerReqest.getUserFirstName() + " " + playerReqest.getUserLastName() + "</b> has been <b>ACCEPTED</b> from " + curUser.getFirstName() + " " + curUser.getLastName() + ".\n";

                                    subject = "[SICS.TV PLAYER REQUEST] Accepted";

                                    MailHelper mail = new MailHelper(SpringApplicationContextHelper.getMessage("email.smtp"), SpringApplicationContextHelper.getMessage("email.smtp.port"), SpringApplicationContextHelper.getMessage("email.smtp.user"), SpringApplicationContextHelper.getMessage("email.smtp.pwd"));
                                    mail.sendMail(SpringApplicationContextHelper.getMessage("email.from"), to, null, "<EMAIL>", null, null, subject, body, null, "");
                                }
                            } catch (Exception ex) {
                                GlobalHelper.reportError(ex);
                            }
                        }
//                        }
                    } catch (ParseException ex) {
                        GlobalHelper.reportError(ex);
                    }
                } else {
                    if (StringUtils.isNotBlank(playerReqest.getStatus())) {
                        status = playerReqest.getStatus();
                    }
                }
            }
        }

        return status;
    }

    @RequestMapping("/denyPlayerRequestTv")
    public @ResponseBody
    String denyPlayerRequestTv(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam("activityId") Long activityId, @RequestParam("agent") String agent,
            @RequestParam("agentEmail") String agentEmail, @RequestParam("customer") String customer) {
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        if (!curUser.hasAccess(3)) {
            return "permissionDenied";
        }
        String status = "ko";

        if (activityId != null) {
            mService.updateVtigerActivityStatus(activityId, "Not Accepted");
            GlobalHelper.writeLogData(session, GlobalHelper.kActionDeclinePlayerRequestTv, curUser.getEmail(), curUser.getPassword(), curUser.getId(), activityId.toString());
            status = "Done";

            // mando mail per dire all'agente che non è stato approvato
            try {
                if (StringUtils.isNotEmpty(agent) && StringUtils.isNotEmpty(agentEmail) && StringUtils.isNotEmpty(customer)) {
                    String to = agentEmail;
                    String subject, body;

                    body = "Dear <b>" + agent + "</b>,<br><br>\n"
                            + "The Match Report Request for <b>" + customer + "</b> has been <b>DECLINED</b> from " + curUser.getFirstName() + " " + curUser.getLastName() + ".";

                    subject = "[SICS.TV PLAYER REQUEST] Not Accepted";

                    MailHelper mail = new MailHelper(SpringApplicationContextHelper.getMessage("email.smtp"), SpringApplicationContextHelper.getMessage("email.smtp.port"), SpringApplicationContextHelper.getMessage("email.smtp.user"), SpringApplicationContextHelper.getMessage("email.smtp.pwd"));
                    mail.sendMail(SpringApplicationContextHelper.getMessage("email.from"), to, null, "<EMAIL>", null, null, subject, body, null, "");
                }
            } catch (Exception ex) {
                GlobalHelper.reportError(ex);
            }
        }

        return status;
    }

    @RequestMapping("/trialSDA")
    public String trialSDA(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam(value = "ignoreLimit", required = false) Boolean ignoreLimit) {
        if (!this.initModule(session, model)) {
            return pageRedirect(pageHome);
        }
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        if (!curUser.hasAccess(3)) {
            return pageRedirect(pageHome);
        }

        List<VtigerTask> assignTeamRequests = mService.getTrialSDARequests(ignoreLimit);
        model.addAttribute("mRequests", assignTeamRequests);
        // CacheInterface.updateTrialRequests(curUser.getId(), tvTrialRequests);

        return pageTrialSDA;
    }

    @RequestMapping("/acceptTrialSDA")
    public @ResponseBody
    String acceptTrialSDA(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam("activityId") Long activityId, @RequestParam("cmsDateEnd") String cmsDateEnd,
            @RequestParam("ignoreLimit") Boolean ignoreLimit) {
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        if (!curUser.hasAccess(2)) {
            return "permissionDenied";
        }
        String status = "ko";
        // non posso caricare dalla cache altrimenti non gestiamo la concorrenza
        List<VtigerTask> trialSDARequests = mService.getTrialSDARequests(ignoreLimit);

        if (activityId != null) {
            VtigerTask trial = null;
            for (VtigerTask task : trialSDARequests) {
                if (task.getId() != null && Long.compare(task.getId(), activityId) == 0) {
                    trial = task;
                    break;
                }
            }

            if (trial != null) {
                if (StringUtils.equalsIgnoreCase(trial.getStatus(), "Planned")) {
                    mService.updateVtigerActivityStatus(activityId, "In Progress");
                    SimpleDateFormat formatter = new SimpleDateFormat("dd/MM/yyyy");
//                    SimpleDateFormat parameterFormatter = new SimpleDateFormat("dd-MM-yyyy");

                    try {
                        Date cmsExpirationDate = formatter.parse(cmsDateEnd);
                        String serialNumber = trial.getSdaSN();
                        Long groupsetId = (trial.getVmUserGroupsetId() != null ? trial.getVmUserGroupsetId() : 426L);
                        AssetResponse assetResponse = AssetHelper.createAsset(trial.getUserToAssign(), 610, serialNumber, groupsetId, cmsExpirationDate, cmsExpirationDate, false, false, false, false, false, false, false, null, null, null);

                        if (assetResponse.getAssetId() != null) {
                            status = "Done";
                            AssetHelper.sendAssetMail(assetResponse.getAssetId());
                        } else {
                            status = StringUtils.defaultIfEmpty(assetResponse.getMessage(), "ko");
                        }

                        if (status.equalsIgnoreCase("Done")) {
                            List<VtigerAsset> assets = mService.getVtigerAssetsByEmail(trial.getUserEmail());
                            if (assets != null && !assets.isEmpty()) {
                                mService.updateVtigerAssetOwnerId(assets.get(0).getAssetId(), trial.getAgentId());
                            }

                            mService.updateVtigerActivityStatus(activityId, "Completed");
                            mService.updateVtigerActivityExpirationDate(activityId, cmsExpirationDate);
                            GlobalHelper.writeLogData(session, GlobalHelper.kActionConfirmTrialSDA, curUser.getEmail(), curUser.getPassword(), curUser.getId(), activityId.toString());

                            // mando mail per dire all'agente che è stato approvato
                            try {
                                if (StringUtils.isNotEmpty(trial.getAgentFirstName()) && StringUtils.isNotEmpty(trial.getAgentLastName())
                                        && StringUtils.isNotEmpty(trial.getAgentEmail())
                                        && StringUtils.isNotEmpty(trial.getUserFirstName()) && StringUtils.isNotEmpty(trial.getUserLastName())) {
                                    String to = trial.getAgentEmail();
                                    String subject, body;

                                    body = "Dear <b>" + trial.getAgentFirstName() + " " + trial.getAgentLastName() + "</b>,<br><br>\n"
                                            + "The SDA Trial Request for <b>" + trial.getUserFirstName() + " " + trial.getUserLastName() + "</b> has been <b>ACCEPTED</b> from " + curUser.getFirstName() + " " + curUser.getLastName() + ".<br>\n"
                                            + "Trial will expire on <b>" + cmsDateEnd + "</b>";

                                    subject = "[SDA TRIAL REQUEST] Accepted";

                                    MailHelper mail = new MailHelper(SpringApplicationContextHelper.getMessage("email.smtp"), SpringApplicationContextHelper.getMessage("email.smtp.port"), SpringApplicationContextHelper.getMessage("email.smtp.user"), SpringApplicationContextHelper.getMessage("email.smtp.pwd"));
                                    mail.sendMail(SpringApplicationContextHelper.getMessage("email.from"), to, null, "<EMAIL>", null, null, subject, body, null, "");
                                }
                            } catch (Exception ex) {
                                GlobalHelper.reportError(ex);
                            }
                        }
                    } catch (ParseException ex) {
                        GlobalHelper.reportError(ex);
                    }
                } else {
                    if (StringUtils.isNotBlank(trial.getStatus())) {
                        status = trial.getStatus();
                    }
                }
            }
        }

        return status;
    }

    @RequestMapping("/denyTrialSDA")
    public @ResponseBody
    String denyTrialSDA(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam("activityId") Long activityId, @RequestParam("agent") String agent,
            @RequestParam("agentEmail") String agentEmail, @RequestParam("customer") String customer) {
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        if (!curUser.hasAccess(3)) {
            return "permissionDenied";
        }
        String status = "ko";

        if (activityId != null) {
            mService.updateVtigerActivityStatus(activityId, "Not Accepted");
            GlobalHelper.writeLogData(session, GlobalHelper.kActionDeclineTrialSDA, curUser.getEmail(), curUser.getPassword(), curUser.getId(), activityId.toString());
            status = "Done";

            // mando mail per dire all'agente che non è stato approvato
            try {
                if (StringUtils.isNotEmpty(agent) && StringUtils.isNotEmpty(agentEmail) && StringUtils.isNotEmpty(customer)) {
                    String to = agentEmail;
                    String subject, body;

                    body = "Dear <b>" + agent + "</b>,<br><br>\n"
                            + "The SDA Trial Request for <b>" + customer + "</b> has been <b>DECLINED</b> from " + curUser.getFirstName() + " " + curUser.getLastName() + ".";

                    subject = "[SDA TRIAL REQUEST] Not Accepted";

                    MailHelper mail = new MailHelper(SpringApplicationContextHelper.getMessage("email.smtp"), SpringApplicationContextHelper.getMessage("email.smtp.port"), SpringApplicationContextHelper.getMessage("email.smtp.user"), SpringApplicationContextHelper.getMessage("email.smtp.pwd"));
                    mail.sendMail(SpringApplicationContextHelper.getMessage("email.from"), to, null, "<EMAIL>", null, null, subject, body, null, "");
                }
            } catch (Exception ex) {
                GlobalHelper.reportError(ex);
            }
        }

        return status;
    }

    @RequestMapping(value = "/fixtureTableData")
    public @ResponseBody
    String fixtureTableData(HttpServletResponse response, ModelMap model, HttpSession session, HttpServletRequest request,
            @RequestParam(value = "countryId", required = false) Long countryId, @RequestParam(value = "competitionId", required = false) Long competitionId,
            @RequestParam(value = "intCompetitionId", required = false) Long intCompetitionId, @RequestParam(value = "matchday", required = false) Long matchday,
            @RequestParam(value = "groupId", required = false) Long groupId, @RequestParam(value = "onlyNotAnalyzed", required = false) Boolean onlyNotAnalyzed,
            @RequestParam(value = "onlyInProgress", required = false) Boolean onlyInProgress, @RequestParam(value = "withVideo", required = false) Boolean withVideo,
            @RequestParam(value = "gameDate", required = false) String gameDate, @RequestParam(value = "priority", required = false) Integer priority) {
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        try {
            List<ManagerGroupset> managerGroupsets = mService.getManagerGroupsets();
            Map<Long, ManagerGroupset> managerGroupsetMap = new HashMap<>();
            Map<Long, List<Long>> competitionGroupsetMap = new HashMap<>();
            for (ManagerGroupset groupset : managerGroupsets) {
                managerGroupsetMap.put(groupset.getId(), groupset);
                if (groupset.getCompetitionList() != null && !groupset.getCompetitionList().isEmpty()) {
                    for (String groupsetCompetitionIdString : groupset.getCompetitionList()) {
                        Long groupsetCompetitionId = Long.valueOf(groupsetCompetitionIdString);
                        competitionGroupsetMap.putIfAbsent(groupsetCompetitionId, new ArrayList<Long>());

                        competitionGroupsetMap.get(groupsetCompetitionId).add(groupset.getId());
                    }
                }
            }

            Date gameDateFilter = null;
            if (StringUtils.isNotBlank(gameDate)) {
                SimpleDateFormat formatter = new SimpleDateFormat("dd/MM/yyyy");
                gameDateFilter = formatter.parse(gameDate);
            }
            List<Fixture> fixtures = CacheInterface.getCache(curUser.getId()).getFixtures();
            if (fixtures != null) {
                List<Short> matchdayFound = new ArrayList<>();
                List<Long> groupFound = new ArrayList<>();
                boolean atLeastOneValid = false;

                StringBuilder json = new StringBuilder("{ \"data\": [");
                if (!fixtures.isEmpty()) {
                    for (Fixture fixture : fixtures) {
                        boolean isValid = true;
                        boolean isValidForAdvancedFilters = false;
                        if (countryId != null) {
                            if (Long.compare(fixture.getCountryId(), countryId) != 0) {
                                isValid = false;
                            }
                        }
                        if (competitionId != null) {
                            if (Long.compare(fixture.getCompetitionId(), competitionId) != 0) {
                                isValid = false;
                            }
                        }
                        if (intCompetitionId != null) {
                            if (Long.compare(fixture.getInternationalCompetitionId(), intCompetitionId) != 0) {
                                isValid = false;
                            }
                        }
                        if (isValid) {
                            if (matchday != null) {
                                if (Long.compare(fixture.getMatchday(), matchday) != 0) {
                                    isValid = false;
                                    isValidForAdvancedFilters = true;
                                }
                            }
                            if (groupId != null) {
                                if (Long.compare(fixture.getGroupId(), groupId) != 0) {
                                    isValid = false;
                                    isValidForAdvancedFilters = true;
                                }
                            }
                        }

                        if (isValid || isValidForAdvancedFilters) {
                            if (fixture.getMatchday() != null && !matchdayFound.contains(fixture.getMatchday())) {
                                matchdayFound.add(fixture.getMatchday());
                            }
                            if (fixture.getGroupId() != null && !groupFound.contains(fixture.getGroupId())) {
                                groupFound.add(fixture.getGroupId());
                            }
                        }

                        // solo partite da analizzare
                        if (isValid && BooleanUtils.isTrue(onlyNotAnalyzed)) {
                            if (fixture.getAnalystId() != null || fixture.getAnalysisLevel() > 0) {
                                isValid = false;
                            }
                        }
                        // solo partite in fase di analisi
                        if (isValid && BooleanUtils.isTrue(onlyInProgress)) {
                            if (fixture.getAnalystId() == null || fixture.getAnalysisLevel() != 0) {
                                isValid = false;
                            }
                        }
                        // solo partite con il video
                        if (isValid && BooleanUtils.isTrue(withVideo)) {
                            if (StringUtils.isBlank(fixture.getVideoName())) {
                                isValid = false;
                            }
                        }
                        // filtro per game date
                        if (isValid && gameDateFilter != null) {
                            if (fixture.getDate() != null) {
                                Calendar cal1 = Calendar.getInstance();
                                Calendar cal2 = Calendar.getInstance();
                                cal1.setTime(fixture.getDate());
                                cal2.setTime(gameDateFilter);
                                boolean sameDay = cal1.get(Calendar.DAY_OF_YEAR) == cal2.get(Calendar.DAY_OF_YEAR)
                                        && cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR);

                                if (!sameDay) {
                                    isValid = false;
                                }
                            }
                        }
                        // filtro per priorità
                        if (isValid && priority != null) {
                            if (priority == -1) {
                                // Priority None -> tutte quelle senza priorità
                                if (fixture.getPriority() != null) {
                                    isValid = false;
                                }
                            } else {
                                if (fixture.getPriority() == null || fixture.getPriority().compareTo(priority) != 0) {
                                    isValid = false;
                                }
                            }
                        }

                        if (isValid) {
                            atLeastOneValid = true;
                            json.append("[");
                            json.append("\"");
                            if (fixture.getVideoName() != null) {
                                if (fixture.getVideoQuality() == 2) {
                                    // full hd
                                    json.append("<i class='me-1'><img src='/sicsmanager/images/full-hd.png'></i>");
                                } else if (fixture.getVideoQuality() == 1) {
                                    // hd
                                    json.append("<i class='me-1'><img src='/sicsmanager/images/hd.png'></i>");
                                } else {
                                    // sd
                                    json.append("<i class='me-1'><img src='/sicsmanager/images/standard.png'></i>");
                                }
                            }
                            if (BooleanUtils.isNotFalse(fixture.getTacticalVideo())) {
                                json.append("<i class='me-1'><img src='/sicsmanager/images/tact.png'></i>");
                            }

                            // immagini
                            if (fixture.getAnalysisLevel() == 1) {
                                // analisi parziale, per SERIE A, SERIE B
                                json.append("<i class='icon-circle2 me-1' style='color: rgba(var(--yellow-rgb),0.8)!important'></i>");
                            } else if (fixture.getAnalyst() == null && fixture.getAnalysisLevel() == 0) {
                                // nessuno sta facendo nulla
                                json.append("<i class='icon-circle me-1'></i>");
                            } else if (fixture.getAnalyst() != null && fixture.getAnalysisLevel() == 0) {
                                // assegnata ad un analista ma non ancora completata
                                json.append("<i class='icon-circle2 me-1' style='color: rgba(var(--warning-rgb),0.8)!important'></i>");
                            } else if (fixture.getAnalysisLevel() > 1) {
                                // fatta da opta o da un analista
                                if (fixture.getAnalysisLevel() == 4) {
                                    // opta
                                    json.append("<i class='icon-circle2 me-1' style='color: rgba(var(--secondary-rgb),0.8)!important'></i>");
                                } else {
                                    json.append("<i class='icon-circle2 me-1' style='color: rgba(var(--success-rgb),0.8)!important'></i>");
                                }
                            }
                            // tasto per fixare partite
                            json.append("<i class='icon-question3 me-1 cursor-pointer' onclick='reimportFixture(").append(fixture.getId()).append(")' data-bs-popup='tooltip' title='Click to re-import fixture from OPTA' data-bs-placement='bottom'></i>");
                            // partita
                            json.append(fixture.getHomeTeam()).append(" - ").append(fixture.getAwayTeam()).append("\",");
                            // analista
                            json.append("\"").append("<u fixtureId=").append(fixture.getId()).append(" class='").append(fixture.getId());
                            if (fixture.getAnalyst() == null) {
                                json.append(" badge bg-primary bg-opacity-20 text-primary p-1'>Click to assign</u>").append("\",");
                            } else {
                                json.append(" badge bg-success bg-opacity-20 text-success p-1'>").append(fixture.getAnalystString()).append("</u>").append("\",");
                            }
                            // priorità
                            json.append("\"").append("<div class='dropdown' fixtureid='").append(fixture.getId()).append("'>");
                            json.append("<a role='button' data-bs-toggle='dropdown'");
                            if (fixture.getPriority() == null || fixture.getPriority() == 0) {
                                json.append(" sort='a' class='badge bg-primary bg-opacity-20 text-primary p-1 dropdown-toggle'>Set Priority</a>");
                            } else {
                                switch (fixture.getPriority()) {
                                    case 1:
                                        json.append(" sort='c' class='badge bg-info bg-opacity-20 text-info p-1 dropdown-toggle'>Low</a>");
                                        break;
                                    case 2:
                                        json.append(" sort='d' class='badge bg-warning bg-opacity-20 text-warning p-1 dropdown-toggle'>Medium</a>");
                                        break;
                                    case 3:
                                        json.append(" sort='e' class='badge bg-warning bg-opacity-20 text-warning p-1 dropdown-toggle'>Medium-High</a>");
                                        break;
                                    case 4:
                                        json.append(" sort='f' class='badge bg-danger bg-opacity-20 text-danger p-1 dropdown-toggle'>High</a>");
                                        break;
                                    case 10:
                                        json.append(" sort='g' class='badge bg-danger bg-opacity-20 text-danger p-1 dropdown-toggle'>High for Everyone</a>");
                                        break;
                                    case 11:
                                        json.append(" sort='h' class='badge bg-danger bg-opacity-20 text-danger p-1 dropdown-toggle'>Very High</a>");
                                        break;
                                    case 20:
                                        json.append(" sort='i' class='badge bg-purple bg-opacity-25 text-purple p-1 dropdown-toggle'>Urgent 1</a>");
                                        break;
                                    case 21:
                                        json.append(" sort='j' class='badge bg-purple bg-opacity-25 text-purple p-1 dropdown-toggle'>Urgent 2</a>");
                                        break;
                                    case 22:
                                        json.append(" sort='k' class='badge bg-purple bg-opacity-25 text-purple p-1 dropdown-toggle'>Urgent 3</a>");
                                        break;
                                    case 23:
                                        json.append(" sort='l' class='badge bg-purple bg-opacity-25 text-purple p-1 dropdown-toggle'>Urgent 4</a>");
                                        break;
                                    case 24:
                                        json.append(" sort='m' class='badge bg-purple bg-opacity-25 text-purple p-1 dropdown-toggle'>Urgent 5</a>");
                                        break;
                                    case 100:
                                        json.append(" sort='b' class='badge bg-secondary bg-opacity-20 text-secondary p-1 dropdown-toggle'>Invalid Game</a>");
                                        break;
                                    default:
                                        break;
                                }
                            }
                            json.append("</div>").append("\",");
//                            json.append("<div class='dropdown-menu dropdown-menu-end'>");
//                            json.append("<a onclick='updateFixturePriority(0, ").append(fixture.getId()).append(")' role='button' class='dropdown-item'><span class='border border-width-4 border-primary rounded-pill me-2'></span>Remove Priority</a>");
//                            json.append("<div class='dropdown-divider'></div>");
//                            json.append("<p class='text-center'>Urgent</p>");
//                            json.append("<div class='btn-toolbar justify-content-center'>").append("<div class='btn-group'>");
//                            json.append("<button onclick='updateFixturePriority(20, ").append(fixture.getId()).append(")' type='button' class='btn bg-purple bg-opacity-50 text-white ms-0 hover-75'>1</button>");
//                            json.append("<button onclick='updateFixturePriority(21, ").append(fixture.getId()).append(")' type='button' class='btn bg-purple bg-opacity-50 text-white ms-0 hover-75'>2</button>");
//                            json.append("<button onclick='updateFixturePriority(22, ").append(fixture.getId()).append(")' type='button' class='btn bg-purple bg-opacity-50 text-white ms-0 hover-75'>3</button>");
//                            json.append("<button onclick='updateFixturePriority(23, ").append(fixture.getId()).append(")' type='button' class='btn bg-purple bg-opacity-50 text-white ms-0 hover-75'>4</button>");
//                            json.append("<button onclick='updateFixturePriority(24, ").append(fixture.getId()).append(")' type='button' class='btn bg-purple bg-opacity-50 text-white ms-0 hover-75'>5</button>");
//                            json.append("</div>").append("</div>");
//                            json.append("<div class='dropdown-divider'></div>");
//                            json.append("<a onclick='updateFixturePriority(11, ").append(fixture.getId()).append(")' role='button' class='dropdown-item'><span class='border border-width-4 border-danger rounded-pill me-2'></span>Very High</a>");
//                            json.append("<a onclick='updateFixturePriority(10, ").append(fixture.getId()).append(")' role='button' class='dropdown-item'><span class='border border-width-4 border-danger rounded-pill me-2'></span>High for Everyone</a>");
//                            json.append("<a onclick='updateFixturePriority(4, ").append(fixture.getId()).append(")' role='button' class='dropdown-item'><span class='border border-width-4 border-danger rounded-pill me-2'></span>High</a>");
//                            json.append("<a onclick='updateFixturePriority(3, ").append(fixture.getId()).append(")' role='button' class='dropdown-item'><span class='border border-width-4 border-warning rounded-pill me-2'></span>Medium-High</a>");
//                            json.append("<a onclick='updateFixturePriority(2, ").append(fixture.getId()).append(")' role='button' class='dropdown-item'><span class='border border-width-4 border-warning rounded-pill me-2'></span>Medium</a>");
//                            json.append("<a onclick='updateFixturePriority(1, ").append(fixture.getId()).append(")' role='button' class='dropdown-item'><span class='border border-width-4 border-info rounded-pill me-2'></span>Low</a>");
//                            json.append("<div class='dropdown-divider'></div>");
//                            json.append("<a onclick='updateFixturePriority(100, ").append(fixture.getId()).append(")' role='button' class='dropdown-item'><span class='border border-width-4 border-secondary rounded-pill me-2'></span>Invalid Game</a>");
//                            json.append("</div></div>").append("\",");
                            // competizione
                            String competitionTooltip = "No one has access";
                            String competitionClass = "text-danger";
                            if (competitionGroupsetMap.containsKey(fixture.getCompetitionId())) {
                                competitionClass = "text-success";
                                competitionTooltip = "";
                                for (Long groupsetId : competitionGroupsetMap.get(fixture.getCompetitionId())) {
                                    if (!competitionTooltip.isEmpty()) {
                                        competitionTooltip += "</br>";
                                    }
                                    competitionTooltip += managerGroupsetMap.get(groupsetId).getName();
                                }
                            }
                            json.append("\"").append(competitionClass).append("|").append(competitionTooltip).append("|").append(fixture.getCompetitionName()).append("\",");
                            // giornata
                            json.append("\"").append(fixture.getMatchday()).append("\",");
                            // data partita
                            json.append("\"").append(fixture.getDateString()).append("\",");
                            // data assegnazione
                            json.append("\"").append(fixture.getAnalystAssignDateString()).append("\",");
                            // reaper
                            json.append("\"").append(fixture.getReaper()).append("\"");
                            json.append("],");
                        }
                    }
                    if (atLeastOneValid) {
                        json.deleteCharAt(json.length() - 1);
                    }
                }
                json.append("]");
                if (!matchdayFound.isEmpty() || !groupFound.isEmpty()) {
                    // tag matchday
                    if (!matchdayFound.isEmpty()) {
                        json.append(",");
                        json.append("\"matchday\": [");
                        for (Short tmpMatchDay : matchdayFound) {
                            json.append(tmpMatchDay).append(",");
                        }
                        if (!matchdayFound.isEmpty()) {
                            json.deleteCharAt(json.length() - 1);
                        }
                        json.append("]");
                    }
                    // tag group
                    if (!groupFound.isEmpty()) {
                        json.append(",");
                        json.append("\"group\": [");
                        for (Long tmpGroupId : groupFound) {
                            json.append(tmpGroupId).append(",");
                        }
                        if (!groupFound.isEmpty()) {
                            json.deleteCharAt(json.length() - 1);
                        }
                    }
                }
                json.append("]}");

                return json.toString();
            } else {
                return "";
            }
        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }
        return "";
    }

    @RequestMapping(value = "/updateFixtureCache")
    public @ResponseBody
    String updateFixtureCache(HttpServletResponse response, ModelMap model, HttpSession session, HttpServletRequest request,
            @RequestParam("seasonIds") String seasonIds, @RequestParam("allFixtures") Boolean allFixtures) {
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        List<Fixture> fixtures = mService.getFixtures(seasonIds, allFixtures, "_en");
        CacheInterface.updateFixtures(curUser.getId(), fixtures);

        return "ok";
    }

    @RequestMapping(value = "/assignFixture")
    public @ResponseBody
    String assignFixture(HttpServletResponse response, ModelMap model, HttpSession session, HttpServletRequest request,
            @RequestParam("data") String data) {
        String status = "ok";
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        if (data != null) {
            try {
                List<User> analysts = mService.getAnalysts();   // carico per avere informazioni sulla lingua
                List<String> games = Arrays.asList(StringUtils.split(data, "|"));
                for (String game : games) {
                    if (game.contains(";")) {
                        List<String> parts = Arrays.asList(StringUtils.split(game, ";"));
                        if (parts.size() == 2) {
                            long fixtureId = Long.parseLong(parts.get(0));
                            long analystId = Long.parseLong(parts.get(1));

                            FixtureAnalyst fixtureAnalyst = mService.getFixtureAnalyst(fixtureId);
                            if (Long.compare(analystId, -1L) == 0) {
                                // delete
                                if (fixtureAnalyst != null && fixtureAnalyst.getUserId() != null) {
                                    mService.deleteAssignedFixture(fixtureId, fixtureAnalyst.getUserId());
                                    GlobalHelper.writeLogData(session, GlobalHelper.kActionDeleteAssignedFixture, curUser.getEmail(), curUser.getPassword(), curUser.getId(), fixtureId + ":" + analystId);

                                    // mando mail per dire che la partita non è più assegnata
                                    sendAnalystMail(curUser, analysts, fixtureAnalyst.getUserId(), fixtureId, 2);
                                }
                            } else {
                                if (fixtureAnalyst != null && fixtureAnalyst.getUserId() != null) {
                                    // update
                                    // mService.updateAssignedFixture(fixtureId, analystId); OLD
                                    mService.assignFixture(fixtureId, analystId);   // questa mette a removed tutte le altre righe prima di fare l'insert
                                    GlobalHelper.writeLogData(session, GlobalHelper.kActionUpdateAssignedFixture, curUser.getEmail(), curUser.getPassword(), curUser.getId(), fixtureId + ":" + analystId);

                                    // mando mail per dire che la partita non è più assegnata
                                    if (Long.compare(analystId, fixtureAnalyst.getUserId()) != 0) {
                                        sendAnalystMail(curUser, analysts, fixtureAnalyst.getUserId(), fixtureId, 2);
                                    }
                                } else {
                                    // insert
                                    mService.assignFixture(fixtureId, analystId);
                                    GlobalHelper.writeLogData(session, GlobalHelper.kActionAssignFixture, curUser.getEmail(), curUser.getPassword(), curUser.getId(), fixtureId + ":" + analystId);
                                }
                                sendAnalystMail(curUser, analysts, analystId, fixtureId, 1);
                            }
                        } else {
                            log.error("Unexpected list size: " + game + " size is: " + parts.size());
                        }
                    } else {
                        log.error("Unexpected string content: " + game);
                    }
                }
            } catch (NumberFormatException ex) {
                status = "ko";
                GlobalHelper.reportError(ex);
            }
        } else {
            status = "ko";
        }
        return status;
    }

    @RequestMapping(value = "/updateFixturePriority")
    public @ResponseBody
    String updateFixturePriority(HttpServletResponse response, ModelMap model, HttpSession session, HttpServletRequest request,
            @RequestParam("fixtureId") Long fixtureId, @RequestParam("priority") Integer priority) {
        if (fixtureId != null && priority != null) {
            mService.updateFixturePriority(fixtureId, priority);
        }

        return "ok";
    }

    @RequestMapping(value = "/updateFixtureData")
    public @ResponseBody
    String updateFixtureData(HttpServletResponse response, ModelMap model, HttpSession session, HttpServletRequest request,
            @RequestParam("fixtureId") Long fixtureId, @RequestParam("what") Integer what,
            @RequestParam(value = "analystId", required = false) Long analystId) {

        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        if (what != null && fixtureId != null) {
            Fixture fixture = mService.getFixture(fixtureId);
            MailHelper mail = new MailHelper(SpringApplicationContextHelper.getMessage("email.smtp"), SpringApplicationContextHelper.getMessage("email.smtp.port"), SpringApplicationContextHelper.getMessage("email.smtp.user"), SpringApplicationContextHelper.getMessage("email.smtp.pwd"));

            switch (what) {
                case 1:
                    // significa che devo segnare la partita come non analizzabile
                    // ma lascio l'assegnazione
                    mService.updateFixturePriority(fixtureId, 100);
                    break;
                case 2:
                    // significa che devo segnare la partita come non analizzabile
                    // e tolgo l'assegnazione
                    mService.updateFixturePriority(fixtureId, 100);
                    if (analystId != null) {
                        boolean removeAssign = true;
                        Date publicationDate = null;
                        List<Fixture> fixturesPublicationDate = mService.getFixturePublicationDate(fixtureId.toString());
                        for (Fixture fixturePublicationDate : fixturesPublicationDate) {
                            if (fixturePublicationDate.getId() != null
                                    && Long.compare(fixtureId, fixturePublicationDate.getId()) == 0) {
                                publicationDate = fixturePublicationDate.getPublicationDate();
                                break;
                            }
                        }
                        if (publicationDate != null) {
                            // se la partita è stata pubblicata non tolgo l'analista
                            // potrebbe succedere che clicco sulla mail troppo tardi e l'analista ha comunque pubblicato
                            removeAssign = false;
                        }
                        if (removeAssign) {
                            mService.deleteAssignedFixture(fixtureId, analystId);
                        }
                    }
                    break;
                case 3:
                    // gestione problemi personali
                    if (analystId != null) {
                        mService.deleteAssignedFixture(fixtureId, analystId);
                    }
                    User analyst = mService.getAnalyst(analystId);
                    if (StringUtils.isNotBlank(analyst.getEmail())) {
                        String subject = "[PERSONAL PROBLEMS] - Confirmation";
                        String body = "Dear <b>" + analyst.getFirstName() + " " + analyst.getLastName() + "</b><br>\n"
                                + "Group Admin / Admin: <b>" + curUser.getFirstName() + " " + curUser.getLastName() + "</b><br>\n"
                                + "<br>\n"
                                + "This email is a confirm that you don't have to analyze this match anymore:\n"
                                + "<br>\n"
                                + "<b>" + fixture.getHomeTeam() + "-" + fixture.getAwayTeam() + " (" + DateHelper.toStringAnalystMail(fixture.getDate()) + ")</b><br>\n"
                                + "Competition: <b>" + fixture.getCompetitionName() + (Long.compare(fixture.getCountryId(), 0) != 0 ? " (" + fixture.getCountryName().toUpperCase() + ")" : "") + "</b><br>\n"
                                + "Match Day: <b>" + fixture.getMatchday() + "</b>";

                        String cc = analyst.getEmail();
                        cc += "|<EMAIL>";
                        cc += "|<EMAIL>";
                        cc += "|<EMAIL>";
                        List<User> groupAdmins = mService.getGroupsetAdmin(analyst.getGroupsetId());
                        if (groupAdmins != null && !groupAdmins.isEmpty()) {
                            for (User groupAdmin : groupAdmins) {
                                if (StringUtils.isNotBlank(groupAdmin.getEmail())) {
                                    cc += "|" + groupAdmin.getEmail();
                                }
                            }
                        }
                        mail.sendMail(SpringApplicationContextHelper.getMessage("email.from"), "<EMAIL>", cc, "", null, null, subject, body, null, "");
                    }
                    break;
                case 4:
                    // il controllore ha deciso che la partita è analizzabile comunque
                    User tmpAnalyst = mService.getAnalyst(analystId);
                    if (StringUtils.isNotBlank(tmpAnalyst.getEmail())) {
                        String subject = "[SUPPORT REQUEST] - Denied";
                        String body = "Dear <b>" + tmpAnalyst.getFirstName() + " " + tmpAnalyst.getLastName() + "</b><br>\n"
                                + "Group Admin / Admin: <b>" + curUser.getFirstName() + " " + curUser.getLastName() + "</b><br>\n"
                                + "<br>\n"
                                + "After a careful check of the video we believe that the conditions for a change of match do not exist, therefore we invite you to complete the analysis of the match in object:\n"
                                + "<br><br>\n"
                                + "<b>" + fixture.getHomeTeam() + "-" + fixture.getAwayTeam() + " (" + DateHelper.toStringAnalystMail(fixture.getDate()) + ")</b><br>\n"
                                + "Competition: <b>" + fixture.getCompetitionName() + (Long.compare(fixture.getCountryId(), 0) != 0 ? " (" + fixture.getCountryName().toUpperCase() + ")" : "") + "</b><br>\n"
                                + "Match Day: <b>" + fixture.getMatchday() + "</b>";

                        String cc = tmpAnalyst.getEmail();
                        cc += "|<EMAIL>";
                        cc += "|<EMAIL>";
                        cc += "|<EMAIL>";
                        List<User> groupAdmins = mService.getGroupsetAdmin(tmpAnalyst.getGroupsetId());
                        if (groupAdmins != null && !groupAdmins.isEmpty()) {
                            for (User groupAdmin : groupAdmins) {
                                if (StringUtils.isNotBlank(groupAdmin.getEmail())) {
                                    cc += "|" + groupAdmin.getEmail();
                                }
                            }
                        }
                        mail.sendMail(SpringApplicationContextHelper.getMessage("email.from"), "<EMAIL>", cc, "", null, null, subject, body, null, "");
                    }
                    break;
                default:
                    break;
            }

            if (what == 1 || what == 2) {
                // mail a noi per informarci che ci sono problemi
                String subject = "[FIXTURE PROBLEMS]", body;
                body = "Group Admin / Admin: <b>" + curUser.getFirstName() + " " + curUser.getLastName() + "</b><br>\n"
                        + "<br>\n"
                        + "Confirmed problems (roster or video) on the following match (#" + fixture.getId() + "):\n"
                        + "<br>\n"
                        + "<b>" + fixture.getHomeTeam() + "-" + fixture.getAwayTeam() + " (" + DateHelper.toStringAnalystMail(fixture.getDate()) + ")</b><br>\n"
                        + "Competition: <b>" + fixture.getCompetitionName() + (Long.compare(fixture.getCountryId(), 0) != 0 ? " (" + fixture.getCountryName().toUpperCase() + ")" : "") + "</b><br>\n"
                        + "Match Day: <b>" + fixture.getMatchday() + "</b><br>";

                String cc = "<EMAIL>";
                cc += "|<EMAIL>";
                cc += "|<EMAIL>";
                if (analystId != null) {
                    User analyst = mService.getAnalyst(analystId);
                    if (analyst != null && analyst.getGroupsetId() != null) {
                        List<User> groupAdmins = mService.getGroupsetAdmin(analyst.getGroupsetId());
                        if (groupAdmins != null && !groupAdmins.isEmpty()) {
                            for (User groupAdmin : groupAdmins) {
                                if (StringUtils.isNotBlank(groupAdmin.getEmail())) {
                                    cc += "|" + groupAdmin.getEmail();
                                }
                            }
                        }
                    }
                }
                mail.sendMail(SpringApplicationContextHelper.getMessage("email.from"), "<EMAIL>", cc, "", null, null, subject, body, null, "");

                if (analystId != null) {
                    User analyst = mService.getAnalyst(analystId);
                    if (StringUtils.isNotBlank(analyst.getEmail())) {
                        subject = "[FIXTURE ISSUES CONFIRMED]";
                        body = "Dear <b>" + analyst.getFirstName() + " " + analyst.getLastName() + "</b><br>\n"
                                + "<br>\n"
                                + "Problems has been confirmed on the following match:\n"
                                + "<br>\n"
                                + "<b>" + fixture.getHomeTeam() + "-" + fixture.getAwayTeam() + " (" + DateHelper.toStringAnalystMail(fixture.getDate()) + ")</b><br>\n"
                                + "Competition: <b>" + fixture.getCompetitionName() + (Long.compare(fixture.getCountryId(), 0) != 0 ? " (" + fixture.getCountryName().toUpperCase() + ")" : "") + "</b><br>\n"
                                + "Match Day: <b>" + fixture.getMatchday() + "</b><br><br>"
                                + "You can now choose a new game to analyze";

                        cc = analyst.getEmail();
                        cc += "|<EMAIL>";
                        cc += "|<EMAIL>";
                        cc += "|<EMAIL>";
                        List<User> groupAdmins = mService.getGroupsetAdmin(analyst.getGroupsetId());
                        if (groupAdmins != null && !groupAdmins.isEmpty()) {
                            for (User groupAdmin : groupAdmins) {
                                if (StringUtils.isNotBlank(groupAdmin.getEmail())) {
                                    cc += "|" + groupAdmin.getEmail();
                                }
                            }
                        }
                        mail.sendMail(SpringApplicationContextHelper.getMessage("email.from"), "<EMAIL>", cc, "", null, null, subject, body, null, "");
                    }
                }
            }
        }

        return "ok";
    }

    @RequestMapping("/games")
    public String games(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam(value = "checkerDate", required = false) String checkerDate, @RequestParam(value = "groupsetId", required = false) Long groupsetId) {
        if (!this.initModule(session, model)) {
            return pageRedirect(pageHome);
        }
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        if (!curUser.hasAccess(9)) {
            return pageRedirect(pageHome);
        }

        // sidebar content
        // daily limit
        String dailyLimitString = curUser.tabComment(9);
        if (curUser.isTabAdmin(9) && StringUtils.isBlank(dailyLimitString)) {
            // in questo modo se imposto il valore prende quello anche se sei admin (per fare test)
            dailyLimitString = "999";
        }
        if (StringUtils.isNotBlank(dailyLimitString)) {
            Long lastDayFixturesAmount = mService.getAnalystFixturesLastDay(curUser.getUserId());

            model.addAttribute("mDailyLimit", dailyLimitString);
            model.addAttribute("mDailyLimitMax", lastDayFixturesAmount);
        }
        // games analyzed
        List<Fixture> analyzedGames = mService.getAnalystFixtureCompleted(Arrays.asList(curUser.getUserId()), curUser.getAllowedCompetitions(), null, null);
        model.addAttribute("mAnalyzedGames", analyzedGames);
        ManagerGroupset groupset = curUser.getGroupset();
        model.addAttribute("mGroupset", groupset);
        List<ManagerGroupset> managerGroupsets = mService.getManagerGroupsets();
        Map<Long, ManagerGroupset> groupsets = new HashMap<>();
        for (ManagerGroupset managerGroupset : managerGroupsets) {
            groupsets.put(managerGroupset.getId(), managerGroupset);
            for (ManagerGroupset userGroupset : curUser.getExtraGroupsets()) {
                if (Long.compare(managerGroupset.getId(), userGroupset.getId()) == 0) {
                    managerGroupset.setHideSalary(BooleanUtils.isTrue(userGroupset.getHideSalary()));
                }
            }
        }
        AnalystHelper.setSalary(analyzedGames, analyzedGames, groupsets);
        double totalSalary = 0;
        for (Fixture fixture : analyzedGames) {
            if (fixture.getSalary() != null) {
                totalSalary += fixture.getSalary();
            }
        }
        model.addAttribute("mEstimatedSalary", totalSalary);

        // group admin list
        List<User> groupAdmins = mService.getGroupsetAdmin(curUser.getGroupsetId());
        model.addAttribute("mGroupsetAdmins", groupAdmins);

        List<ManagerGroupset> groups = new ArrayList<>(curUser.getExtraGroupsets());
        if (!curUser.isTabAdmin(9)) {
            boolean userGroupsetFound = false;
            for (ManagerGroupset group : groups) {
                if (Long.compare(group.getId(), curUser.getGroupsetId()) == 0) {
                    userGroupsetFound = true;
                }
            }
            if (!userGroupsetFound) {
                groups.add(curUser.getGroupset());
            }
        } else {
            groups = managerGroupsets;
        }

        // daily work checker
        model.addAttribute("mUserGroupsets", groups);
//        if (groupsetId == null) {
//            if (curUser.tabRole(9) == 2) {
//                groupsetId = curUser.getGroupsetId();
//            }
//        }
        model.addAttribute("mCheckerGroupsetId", groupsetId);
        List<User> analysts = mService.getAnalysts(null);
        if (!curUser.isTabAdmin(9)) {
            List<User> analystsToRemove = new ArrayList<>();
            for (User analyst : analysts) {
                if (analyst.getGroupsetId() != null) {
                    // se non passo il gruppo (per la tab "Games by Day") mostro tutti quelli che posso vedere
                    // se lo passo allora tolgo tutti quelli che non fanno parte di quel gruppo
                    if (groupsetId == null) {
                        if (!curUser.hasAccessToGroupset(analyst.getGroupsetId())) {
                            analystsToRemove.add(analyst);
                        }
                    } else if (Long.compare(analyst.getGroupsetId(), groupsetId) != 0) {
                        analystsToRemove.add(analyst);
                    }
                } else {
                    analystsToRemove.add(analyst);
                }
            }
            analysts.removeAll(analystsToRemove);
        }
        Map<User, List<Fixture>> analystDailyWorkMap = new HashMap<>();
        List<Fixture> todayFixtures = mService.getTodayFixtures(null, checkerDate);
        int totalFixtures = 0;
        for (User analyst : analysts) {
            analystDailyWorkMap.putIfAbsent(analyst, new ArrayList<Fixture>());
            if (todayFixtures != null && !todayFixtures.isEmpty()) {
                for (Fixture fixture : todayFixtures) {
                    if (fixture.getAnalystId() != null && Long.compare(fixture.getAnalystId(), analyst.getUserId()) == 0) {
                        analystDailyWorkMap.get(analyst).add(fixture);
                        totalFixtures++;
                    }
                }
            }
        }
        model.addAttribute("mCheckerDate", checkerDate);

        // SORT DELLA MAPPA
        // Step 1: Create a list from the map's entry set
        List<Map.Entry<User, List<Fixture>>> entryList = new ArrayList<>(analystDailyWorkMap.entrySet());
        // Step 2: Sort the list based on the size of the lists
        Collections.sort(entryList, new Comparator<Map.Entry<User, List<Fixture>>>() {
            @Override
            public int compare(Map.Entry<User, List<Fixture>> entry1, Map.Entry<User, List<Fixture>> entry2) {
                if (entry1.getValue().size() == entry2.getValue().size()) {
                    return entry1.getKey().getFirstName().compareTo(entry2.getKey().getFirstName());
                }
                return Integer.compare(entry1.getValue().size(), entry2.getValue().size());
            }
        });
        // Step 3: Create a LinkedHashMap to maintain the sorted order
        Map<User, List<Fixture>> sortedMap = new LinkedHashMap<>();
        for (Map.Entry<User, List<Fixture>> entry : entryList) {
            sortedMap.put(entry.getKey(), entry.getValue());
        }
        model.addAttribute("mTodayFixtures", sortedMap);
        model.addAttribute("mTodayFixtureAmount", totalFixtures);

        return pageGames;
    }

    @RequestMapping(value = "/gamesTableData")
    public @ResponseBody
    String gamesTableData(HttpServletResponse response, ModelMap model, HttpSession session, HttpServletRequest request,
            @RequestParam(value = "allFixtures", required = false) Boolean allFixtures, @RequestParam(value = "onlyToDo", required = false) Boolean onlyToDo,
            @RequestParam(value = "inProgress", required = false) Boolean inProgress, @RequestParam(value = "withVideo", required = false) Boolean withVideo) {
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        try {
            List<Fixture> fixtures = mService.getFixturesForAnalyst(curUser.getAllowedCompetitions(), curUser.isTabAdmin(9));
            if (fixtures != null) {
                StringBuilder json = new StringBuilder("{ \"data\": [");
                if (!fixtures.isEmpty()) {
                    boolean atLeastOneValid = false;
                    for (Fixture fixture : fixtures) {
                        boolean isValid = false;
                        if (curUser.isTabAdmin(9) || curUser.tabRole(9) == 2) { // supervisore
                            if (BooleanUtils.isTrue(allFixtures)) { // calendario
                                isValid = true;
                            } else if (fixture.getDate().before(new Date())) { // mostro le partite vecchie senza video
                                isValid = true;
                            }
                        } else { // analista
                            if (fixture.getAnalystId() == null || (Long.compare(fixture.getAnalystId(), curUser.getUserId()) == 0 && fixture.getAnalysisLevel() == 0)) {
                                // se non ha analista o se sono io ma devo ancora consegnare
                                if (BooleanUtils.isTrue(allFixtures)) { // calendario
                                    isValid = true;
                                } else if (fixture.getDate().before(new Date())) { // mostro le partite vecchie senza video
                                    isValid = true;
                                }

                                // se la priorità è a 100 e non c'è analista o non sono io nascondo
                                if (fixture.getPriority() != null && fixture.getPriority() == 100
                                        && (fixture.getAnalystId() == null || Long.compare(fixture.getAnalystId(), curUser.getUserId()) != 0)) {
                                    isValid = false;
                                }
                            }
                        }
                        // per controllori e admin c'è checkbox per mostrare solo le partite da fare
                        if (BooleanUtils.isTrue(onlyToDo)) {
                            if (fixture.getAnalystId() != null || fixture.getAnalysisLevel() > 0) {
                                isValid = false;
                            }
                        }
                        if (BooleanUtils.isTrue(inProgress)) {
                            if (fixture.getAnalystId() == null || fixture.getAnalysisLevel() != 0) {
                                isValid = false;
                            }
                        }
                        // solo partite con il video
                        if (isValid && BooleanUtils.isTrue(withVideo)) {
                            if (StringUtils.isBlank(fixture.getVideoName())) {
                                isValid = false;
                            }
                        }

                        // non mostro più le partite invalide
                        if (fixture.getPriority() != null) {
                            if (Integer.compare(fixture.getPriority(), 100) == 0) {
                                isValid = false;
                            }
                        }

                        if (isValid) {
                            atLeastOneValid = true;
                            json.append("[");
                            json.append("\"");
                            if (fixture.getVideoName() != null) {
                                if (fixture.getVideoQuality() == 2) {
                                    // full hd
                                    json.append("<i class='me-1'><img src='/sicsmanager/images/full-hd.png'></i>");
                                } else if (fixture.getVideoQuality() == 1) {
                                    // hd
                                    json.append("<i class='me-1'><img src='/sicsmanager/images/hd.png'></i>");
                                } else {
                                    // sd
                                    json.append("<i class='me-1'><img src='/sicsmanager/images/standard.png'></i>");
                                }
                            }
                            if (BooleanUtils.isNotFalse(fixture.getTacticalVideo())) {
                                json.append("<i class='me-1'><img src='/sicsmanager/images/tact.png'></i>");
                            }

                            // partita
                            // per gli analisti segno la partita che stanno facendo in giallo
                            String analystIdContent = "";
                            if (fixture.getAnalystId() != null) {
                                if (!(curUser.isTabAdmin(9) || curUser.tabRole(9) == 2)) {
                                    analystIdContent = "userId='" + fixture.getAnalystId() + "'";
                                } else {
                                    if (fixture.getAnalysisLevel() == 0) {
                                        analystIdContent = "analystId='" + fixture.getAnalystId() + "'";
                                    } else {
                                        analystIdContent = "analystIdCompleted='" + fixture.getAnalystId() + "'";
                                    }
                                }
                            }
                            json.append(fixture.getHomeTeam()).append(" - ").append(fixture.getAwayTeam())
                                    .append("<div class='text-muted fs-sm fixture-id'")
                                    .append(analystIdContent)
                                    .append(">#").append(fixture.getId())
                                    .append("</div>")
                                    .append("\",");
                            // disponibilità video
                            if (fixture.getVideoName() != null) {
                                json.append("\"").append("<span class='badge bg-info bg-opacity-20 text-info p-1 d-block'>Yes</span>").append("\",");
                            } else {
                                json.append("\"").append("<span class='badge bg-warning bg-opacity-20 text-warning p-1 d-block'>No</span>").append("\",");
                            }
                            // competizione
                            json.append("\"").append(fixture.getCompetitionName())
                                    .append(StringUtils.isNotBlank(fixture.getGroupName()) ? (" (GROUP " + fixture.getGroupName() + ")") : "")
                                    .append("<div class='text-muted fs-sm'>")
                                    .append(StringUtils.defaultIfEmpty(fixture.getCountryName(), fixture.getInternationalCompetitionName()))
                                    .append("</div>")
                                    .append("\",");
                            // data partita + giornata
                            json.append("\"").append(fixture.getDateString())
                                    .append("<div class='text-muted fs-sm'>Matchday: ")
                                    .append(fixture.getMatchday())
                                    .append("</div>")
                                    .append("\",");
                            // priorità
                            json.append("\"").append("<span");
                            if (fixture.getPriority() == null || fixture.getPriority() == 0) {
                                json.append(" order='a'>None</span>");
                            } else { // non mostro partite invalide
                                switch (fixture.getPriority()) {
                                    case 1:
                                        json.append(" class='badge bg-info bg-opacity-20 text-info p-1 d-block' order='b'>Low</span>");
                                        break;
                                    case 2:
                                        json.append(" class='badge bg-warning bg-opacity-20 text-warning p-1 d-block' order='c'>Medium</span>");
                                        break;
                                    case 3:
                                        json.append(" class='badge bg-warning bg-opacity-20 text-warning p-1 d-block' order='d'>Medium-High</span>");
                                        break;
                                    case 4:
                                    case 10:
                                        json.append(" class='badge bg-danger bg-opacity-20 text-danger p-1 d-block' order='e'>High</span>");
                                        break;
                                    case 11:
                                        json.append(" class='badge bg-danger bg-opacity-20 text-danger p-1 d-block' order='f'>Very High</span>");
                                        break;
                                    case 20:
                                        json.append(" class='badge bg-purple bg-opacity-25 text-purple p-1 d-block' order='g'>Urgent 1</a>");
                                        break;
                                    case 21:
                                        json.append(" class='badge bg-purple bg-opacity-25 text-purple p-1 d-block' order='h'>Urgent 2</a>");
                                        break;
                                    case 22:
                                        json.append(" class='badge bg-purple bg-opacity-25 text-purple p-1 d-block' order='i'>Urgent 3</a>");
                                        break;
                                    case 23:
                                        json.append(" class='badge bg-purple bg-opacity-25 text-purple p-1 d-block' order='j'>Urgent 4</a>");
                                        break;
                                    case 24:
                                        json.append(" class='badge bg-purple bg-opacity-25 text-purple p-1 d-block' order='k'>Urgent 5</a>");
                                        break;
                                    case 100:
                                        json.append(" class='badge bg-secondary bg-opacity-20 text-secondary p-1 d-block' order='l'>Invalid Game</span>");
                                        break;
                                    default:
                                        break;
                                }
                            }
                            json.append("\",");
                            // analista
                            if (curUser.isTabAdmin(9) || curUser.tabRole(9) == 2) {
                                // se supervisore vedo anche le colonne degli analisti
                                json.append("\"").append("<span class='");
                                if (fixture.getAnalyst() == null) {
                                    json.append("'></span>");
                                } else {
                                    List<String> tooltipContent = new ArrayList<>();
                                    if (StringUtils.isNotBlank(fixture.getVideoPublicationDateString())) {
                                        tooltipContent.add("Video: " + fixture.getVideoPublicationDateString());
                                    }
                                    if (StringUtils.isNotBlank(fixture.getAnalystAssignDateString())) {
                                        tooltipContent.add("Assign: " + fixture.getAnalystAssignDateString());
                                    }
                                    if (fixture.getAnalysisLevel() != null && fixture.getAnalysisLevel() > 0 && fixture.getAnalysisLevel() != 3) {
                                        tooltipContent.add("Commit: " + fixture.getPublicationDateString());
                                    }
                                    String tooltip = "";
                                    if (!tooltipContent.isEmpty()) {
                                        tooltip = "data-bs-popup='tooltip' data-bs-html='true' data-bs-placement='bottom'";
                                        if (tooltipContent.size() == 3) {
                                            // se ci sono entrambe le righe allora metto anche il tempo che ci ha messo
                                            long differenceInMillis = fixture.getPublicationDate().getTime() - fixture.getAnalystAssignDate().getTime();
                                            long hours = TimeUnit.MILLISECONDS.toHours(differenceInMillis);
                                            long minutes = TimeUnit.MILLISECONDS.toMinutes(differenceInMillis) % 60;
                                            // long seconds = TimeUnit.MILLISECONDS.toSeconds(differenceInMillis) % 60;
                                            tooltipContent.add("Elapsed: " + hours + " hours and " + minutes + " minutes");
                                        }
                                        String content = "";
                                        for (String line : tooltipContent) {
                                            if (!content.isEmpty()) {
                                                content += "</br>";
                                            }
                                            content += "<small>" + line + "</small>";
                                        }

                                        tooltip += "title='" + content + "'";
                                    }
                                    json.append(" badge bg-info bg-opacity-20 text-info p-1 w-100' ").append(tooltip).append(">").append(fixture.getAnalystString());
                                    if (StringUtils.isNotBlank(fixture.getAnalystAssignDateString())) {
                                        json.append("<div class='text-secondary fs-sm'>").append(fixture.getAnalystAssignDateString()).append("</div>");
                                    }
                                    json.append("</span>");
                                }

                                // informazioni (se analizzata e data pubblicazione)
                                if (fixture.getAnalysisLevel() != null && fixture.getAnalysisLevel() > 0 && fixture.getAnalysisLevel() != 3) {
                                    String color = "success";
                                    if (fixture.getPriority() != null) {
                                        if (fixture.getPriority() <= 3) {
                                            if (fixture.getPublicationDate() != null && fixture.getAnalystAssignDate() != null) {
                                                long differenceInMillis = fixture.getPublicationDate().getTime() - fixture.getAnalystAssignDate().getTime();
                                                long hours = TimeUnit.MILLISECONDS.toHours(differenceInMillis);
                                                // massimo 8 ore per farla
                                                if (hours > 8) {
                                                    color = "danger";
                                                }
                                            }
                                        } else if (fixture.getPriority() == 4 || fixture.getPriority() == 10 || fixture.getPriority() == 11) {
                                            // per priorità High e Very High
                                            if (fixture.getAnalystAssignDate() != null) {
                                                Calendar calendar = Calendar.getInstance();
                                                calendar.setTime(fixture.getAnalystAssignDate());
                                                int hour = calendar.get(Calendar.HOUR_OF_DAY);

                                                if (hour >= 10 && hour <= 22) {
                                                    // tra le 10 di mattina e le 22 di sera hanno 7 ore per farla
                                                    long differenceInMillis = fixture.getPublicationDate().getTime() - fixture.getAnalystAssignDate().getTime();
                                                    long hours = TimeUnit.MILLISECONDS.toHours(differenceInMillis);
                                                    // massimo 8 ore per farla
                                                    if (hours > 7) {
                                                        color = "danger";
                                                    }
                                                } else {
                                                    // tra le 23 di sera e le 9 di mattina hanno tempo fino alle 14
                                                    calendar.setTime(fixture.getPublicationDate());
                                                    hour = calendar.get(Calendar.HOUR_OF_DAY);
                                                    if (hour > 14) {
                                                        color = "danger";
                                                    }
                                                }
                                            }
                                        }
                                    }

                                    json.append("<span class='badge bg-").append(color).append(" bg-opacity-20 text-").append(color).append(" p-1 w-100'>Published</span>").append("\",");
                                } else if (fixture.getAnalystId() != null) {
                                    json.append("<span class='badge bg-warning bg-opacity-20 text-warning p-1 w-100'>In Progress</span>").append("\",");
                                } else {
                                    json.append("\",");
                                }
                            }
                            // azioni
//                            String noteContent;
//                            if (StringUtils.isNotBlank(fixture.getNote())) {
//                                noteContent = "<a role='button' data-bs-popup='tooltip' data-bs-original-title='View match notes' onclick='loadNotes(" + fixture.getId() + ")'><i class='ph-note-pencil'></i></a>";
//                            } else {
//                                noteContent = "<a role='button' data-bs-popup='tooltip' data-bs-original-title='Add notes to match' onclick='loadNotes(" + fixture.getId() + ")'><i class='ph-note-pencil'></i></a>";
//                            }
//                            if (StringUtils.isNotBlank(fixture.getVideoName())) {
//                                if (fixture.getAnalysisLevel() == 0 && fixture.getAnalystId() == null) {
//                                    json.append("\"").append("<u class='badge bg-primary bg-opacity-20 text-primary d-block p-1 cursor-pointer' onclick='autoAssignGame(").append(fixture.getId()).append(")'>Click to assign</u>");
//                                } else {
//                                    if (!(curUser.isTabAdmin(9) || curUser.tabRole(9) == 2) && (fixture.getAnalystId() != null && Long.compare(fixture.getAnalystId(), curUser.getUserId()) == 0)) {
//                                        // se analista e sono io scrivo che devo consegnarla
//                                        json.append("\"").append("<u class='badge bg-success bg-opacity-20 text-success d-block p-1'>Match Assigned</u>");
//                                    } else {
//                                        json.append("\"");
//                                    }
//                                }
//                            } else {
//                                json.append("\"");
//                            }
//                            // azioni -> note
//                            json.append("<u class='text-muted fs-sm'>Notes</u>");
//                            json.append("\"");

                            json.deleteCharAt(json.length() - 1); // tolgo ultima virgola
                            json.append("],");
                        }
                    }
                    if (atLeastOneValid) {
                        json.deleteCharAt(json.length() - 1);
                    }
                }
                json.append("],");
                // fixtures objects
                json.append("\"fixtures\": [");
                if (!fixtures.isEmpty()) {
                    for (Fixture fixture : fixtures) {
                        json.append(fixture.getJson()).append(",");
                    }
                    json.deleteCharAt(json.length() - 1);
                }
                json.append("]");
                json.append("}");

                return json.toString();
            } else {
                return "";
            }
        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }
        return "";
    }

    @RequestMapping(value = "/saveFixtureNote")
    public @ResponseBody
    String saveFixtureNote(HttpServletResponse response, ModelMap model, HttpSession session, HttpServletRequest request,
            @RequestParam("fixtureId") Long fixtureId, @RequestParam("analystId") Long analystId,
            @RequestParam("note") String note) {
        String result = "";

        if (fixtureId != null) {
            mService.saveFixtureNote(fixtureId, analystId, note);
            result = "ok";
        } else {
            result = "Something went wrong";
        }

        return result;
    }

    @RequestMapping(value = "/saveAnalystFixtureNote")
    public @ResponseBody
    String saveAnalystFixtureNote(HttpServletResponse response, ModelMap model, HttpSession session, HttpServletRequest request,
            @RequestParam("fixtureId") Long fixtureId, @RequestParam("note") String note) {
        String result = "";

        if (fixtureId != null) {
            mService.saveFixtureAnalystNote(fixtureId, 1, note);
            result = "ok";
        } else {
            result = "Something went wrong";
        }

        return result;
    }

    @RequestMapping(value = "/contactSupport")
    public @ResponseBody
    String contactSupport(HttpServletResponse response, ModelMap model, HttpSession session, HttpServletRequest request,
            @RequestParam("fixtureId") Long fixtureId, @RequestParam("type") Long type, @RequestParam(value = "cc", required = false) String cc,
            @RequestParam(value = "description", required = false) String description, @RequestParam(value = "addToNotes", required = false) Boolean addToNotes) {
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        String result = "";

        if (type != null) {
            if (fixtureId != null) {
                Fixture fixture = mService.getFixtureForAnalyst(fixtureId, curUser.getUserId());
                if (fixture != null) {
                    String subject = "", body = "";
                    if (type == 1) {
                        subject = "[SUPPORT REQUIRED] - Video Bad Quality / Wrong Video";
                    } else if (type == 2) {
                        subject = "[SUPPORT REQUIRED] - Missing Roster";
                    } else if (type == 3) {
                        subject = "[SUPPORT REQUIRED] - Holes in Video";
                    } else if (type == 4) {
                        subject = "[SUPPORT REQUIRED] - Personal Problems";
                    }

                    String resetFixtureUrl = "", validFixtureUrl = "";
                    if (request.getServerName().startsWith("192")) {
                        // per farlo andare in locale
                        resetFixtureUrl = "http://" + request.getServerName() + ":8084/sicsmanager/user/updateFixtureData.htm";
                        validFixtureUrl = "http://" + request.getServerName() + ":8084/sicsmanager/user/updateFixtureData.htm?fixtureId=" + fixtureId + "&what=4&analystId=" + curUser.getUserId();
                    } else {
                        resetFixtureUrl = "https://" + request.getServerName() + "/sicsmanager/user/updateFixtureData.htm";
                        validFixtureUrl = "https://" + request.getServerName() + "/sicsmanager/user/updateFixtureData.htm?fixtureId=" + fixtureId + "&what=4&analystId=" + curUser.getUserId();
                    }
                    if (type != 4) {
                        resetFixtureUrl += "?fixtureId=" + fixtureId + "&what=2&analystId=" + curUser.getUserId();
                    } else {
                        resetFixtureUrl += "?fixtureId=" + fixtureId + "&what=3&analystId=" + curUser.getUserId();
                    }
//                    try {
//                        resetFixtureUrl = URLEncoder.encode(resetFixtureUrl, StandardCharsets.UTF_8.toString());
//                    } catch (UnsupportedEncodingException ex) {
//                        GlobalHelper.reportError(ex);
//                    }

                    description = GlobalHelper.removeUnwantedCharacter(StringUtils.defaultIfEmpty(description, ""), true);

                    body = "Analyst: <b>" + curUser.getFirstName() + " " + curUser.getLastName() + "</b><br>\n"
                            + "<br>\n"
                            + "Support has been required for the following match:\n"
                            + "<br>\n"
                            + "<b>" + fixture.getHomeTeam() + "-" + fixture.getAwayTeam() + " (" + DateHelper.toStringAnalystMail(fixture.getDate()) + ")</b><br>\n"
                            + "Competition: <b>" + fixture.getCompetitionName() + (Long.compare(fixture.getCountryId(), 0) != 0 ? " (" + fixture.getCountryName().toUpperCase() + ")" : "") + "</b><br>\n"
                            + "Match Day: <b>" + fixture.getMatchday() + "</b><br>"
                            + "Analyst Message: " + description + "<br>"
                            + "Analyst Email: " + (StringUtils.defaultIfEmpty(curUser.getEmail(), "")) + "<br><br>";

                    // gestione problemi personali
                    if (type != 4) {
                        body += "<a href=\"" + resetFixtureUrl + "\">Click here to set fixture as not analyzable and let analyst choose another game</a>";
                        // di default metto sempre la descrizione nelle note per le partite invalide
                        addToNotes = true;
                    } else {
                        body += "<a href=\"" + resetFixtureUrl + "\">Click here to remove the game to the analyst</a>";
                    }
                    body += "<br><a href=\"" + validFixtureUrl + "\">Click here to tell analyst that the game is still analyzable</a>";

                    cc = StringUtils.defaultIfEmpty(cc, "");
                    if (cc.length() > 0) {
                        cc += "|";
                    }
                    cc += "<EMAIL>";
                    cc += "|<EMAIL>";
                    cc += "|<EMAIL>";

                    MailHelper mail = new MailHelper(SpringApplicationContextHelper.getMessage("email.smtp"), SpringApplicationContextHelper.getMessage("email.smtp.port"), SpringApplicationContextHelper.getMessage("email.smtp.user"), SpringApplicationContextHelper.getMessage("email.smtp.pwd"));
                    mail.sendMail(SpringApplicationContextHelper.getMessage("email.from"), "<EMAIL>", cc, "", null, null, subject, body, null, "");

                    GlobalHelper.writeLogData(session, GlobalHelper.kActionSupportRequired, curUser.getEmail(), curUser.getPassword(), curUser.getId(), curUser.getUserId() + ":" + fixtureId);

                    if (BooleanUtils.isTrue(addToNotes)) {
                        String note = StringUtils.defaultIfEmpty(fixture.getNote(), "");
                        if (!note.isEmpty()) {
                            note += "\n\n";
                        }
                        note += "SUPPORT REQUEST:\n";
                        note += StringUtils.defaultIfEmpty(description, "");
                        mService.saveFixtureNote(fixtureId, curUser.getUserId(), note);
                    }

                    result = "ok";
                } else {
                    result = "Unexpected Error (0x0B)";
                }
            } else {
                result = "Unexpected Error (0x0A)";
            }
        } else {
            result = "Something went wrong";
        }

        return result;
    }

    @RequestMapping(value = "/autoAssignGame")
    public @ResponseBody
    String autoAssignGame(HttpServletResponse response, ModelMap model, HttpSession session, HttpServletRequest request,
            @RequestParam("fixtureId") Long fixtureId) {
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        String result = "ko";

        if (fixtureId != null) {
            Fixture desideredFixture = mService.getFixture(fixtureId);
            if (desideredFixture != null) {
                // 1. deve avere il video
                if (StringUtils.isNotBlank(desideredFixture.getVideoName())) {
                    if (curUser.hasAccessToFixture(desideredFixture)) {
                        // 2. non ho altre partite assegnate che devo ancora finire
                        List<Long> fixtureIdToCommit = mService.getAnalystFixtureNotCompleted(curUser.getUserId(), curUser.getAllowedCompetitions());
                        if (fixtureIdToCommit == null || fixtureIdToCommit.isEmpty()) {
                            // 3. limite giornaliero di X partite
                            String dailyLimitString = curUser.tabComment(9);
                            if (curUser.isTabAdmin(9) && StringUtils.isBlank(dailyLimitString)) {
                                // in questo modo se imposto il valore prende quello anche se sei admin (per fare test)
                                dailyLimitString = "999";
                            }
                            if (StringUtils.isNotBlank(dailyLimitString)) {
                                Long dailyLimit = Long.valueOf(dailyLimitString);
                                Long lastDayFixturesAmount = mService.getAnalystFixturesLastDay(curUser.getUserId());
                                if (lastDayFixturesAmount != null) {
                                    if (lastDayFixturesAmount < dailyLimit) {
                                        Long maxPriority = mService.getAnalystFixtureMaxPriority(curUser.getAllowedCompetitions());
                                        if (desideredFixture.getPriority() == null) {
                                            desideredFixture.setPriority(0);
                                        }
                                        // gestisco caso priorità High (4) e priorità massima High for Everyone (10)
                                        if (desideredFixture.getPriority() >= maxPriority || (maxPriority == 10 && desideredFixture.getPriority() == 4)) {
                                            if (desideredFixture.getAnalystId() == null) {
                                                mService.assignFixture(fixtureId, curUser.getUserId());
                                                GlobalHelper.writeLogData(session, GlobalHelper.kActionAutoAssignGame, curUser.getEmail(), curUser.getPassword(), curUser.getId(), curUser.getUserId() + ":" + fixtureId);
                                                result = "ok";
                                            } else {
                                                result = "Someone else has already taken this game";
                                            }
                                        } else {
                                            result = "You must choose the games with the higher priority first";
                                        }
                                    } else {
                                        result = "Daily Limit Exceeded (" + dailyLimit + " games)";
                                    }
                                } else {
                                    result = "Something went wrong (0x0B)";
                                }
                            } else {
                                result = "Something went wrong (0x0A)";
                            }
                        } else {
                            result = "You need to commit your latest assigned game first";
                        }
                    } else {
                        result = "You dont have access to this game";
                    }
                } else {
                    result = "Game video is not available";
                }
            } else {
                result = "Unable to find selected game";
            }
        } else {
            result = "Something went wrong";
        }

        return result;
    }

    @RequestMapping("/gamesReport")
    public String gamesReport(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response) {
        if (!this.initModule(session, model)) {
            return pageRedirect(pageHome);
        }
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        if (!curUser.hasAccess(9)) {
            return pageRedirect(pageHome);
        }

        if (curUser.isTabAdmin(9)) {
            List<User> analysts = mService.getAnalysts(null);
            model.addAttribute("mAnalysts", analysts);

            List<ManagerGroupset> groups = mService.getManagerGroupsets();
            model.addAttribute("mGroups", groups);
        } else if (curUser.tabRole(9) == 2) {
            List<User> analysts = mService.getAnalysts(null);
            List<User> analystsToRemove = new ArrayList<>();
            for (User analyst : analysts) {
                if (analyst.getGroupsetId() != null) {
                    if (!curUser.hasAccessToGroupset(analyst.getGroupsetId())) {
                        analystsToRemove.add(analyst);
                    }
                } else {
                    analystsToRemove.add(analyst);
                }
            }
            analysts.removeAll(analystsToRemove);

            model.addAttribute("mAnalysts", analysts);
            List<ManagerGroupset> groups = new ArrayList<>(curUser.getExtraGroupsets());
            boolean userGroupsetFound = false;
            for (ManagerGroupset group : groups) {
                if (Long.compare(group.getId(), curUser.getGroupsetId()) == 0) {
                    userGroupsetFound = true;
                }
            }
            if (!userGroupsetFound) {
                groups.add(curUser.getGroupset());
            }
            model.addAttribute("mGroups", groups);
        }

        List<String> lastThreeMonths = new ArrayList<>();
        LocalDate currentDate = LocalDate.now();
        for (int i = 0; i < 4; i++) {
            // Subtract months to get the last three months
            LocalDate month = currentDate.minusMonths(i);
            // Get the full month name
            String monthName = month.getMonth().getDisplayName(TextStyle.FULL, Locale.ENGLISH);
            lastThreeMonths.add(monthName);
        }
        model.addAttribute("mMonths", lastThreeMonths);

        return pageGamesReport;
    }

    @RequestMapping("/gamesReportData")
    public String gamesReportData(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam("analystId") Long analystId, @RequestParam("groupsetIds") String groupsetIds,
            @RequestParam("option") Integer option) {
        if (!this.initModule(session, model)) {
            return "";
        }
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        if (!curUser.hasAccess(9)) {
            return "";
        }

        List<Long> analystIds = null;
        boolean validRequest = false;
        if (analystId == null) {
            if (curUser.isTabAdmin(9) || curUser.tabRole(9) == 2) {
                validRequest = true;
                if (groupsetIds == null && curUser.tabRole(9) == 2) {
                    analystIds = new ArrayList<>();
                    for (Long userGroupsetId : curUser.getUserGroupsets()) {
                        List<User> groupsetAnalysts = mService.getUsersByGroupset(userGroupsetId);
                        if (!groupsetAnalysts.isEmpty()) {
                            for (User analyst : groupsetAnalysts) {
                                if (analyst.getUserId() != null) {
                                    analystIds.add(analyst.getUserId());
                                }
                            }
                        } else {
                            validRequest = false;
                        }
                    }
                }

                if (StringUtils.isNotBlank(groupsetIds)) {
                    List<String> groupsetIdList = Arrays.asList(StringUtils.split(groupsetIds, ","));
                    analystIds = new ArrayList<>();
                    for (String groupsetIdString : groupsetIdList) {
                        Long groupsetId = Long.valueOf(groupsetIdString);
                        if (curUser.isTabAdmin(9) || curUser.hasAccessToGroupset(groupsetId)) {
                            List<User> groupsetAnalysts = mService.getUsersByGroupset(groupsetId);
                            if (!groupsetAnalysts.isEmpty()) {
                                for (User analyst : groupsetAnalysts) {
                                    if (analyst.getUserId() != null) {
                                        analystIds.add(analyst.getUserId());
                                    }
                                }
                            } else {
                                validRequest = false;
                                break;
                            }
                        }
                    }
                }
            } else {
                analystIds = Arrays.asList(curUser.getUserId());
                analystId = curUser.getUserId();
                validRequest = true;
            }
        } else {
            analystIds = Arrays.asList(analystId);
            validRequest = true;
        }

        if (validRequest && option != null) {
            // devo prendere i dati degli ultimi 3 mesi per calcolare penalità e bonus
            Date to = new Date();
            if (option == 2) {
                Calendar calendar = Calendar.getInstance();
                calendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH) - 1);
                calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
                calendar.set(Calendar.HOUR_OF_DAY, 23);
                calendar.set(Calendar.MINUTE, 59);
                calendar.set(Calendar.SECOND, 59);
                calendar.set(Calendar.MILLISECOND, 999);
                to = calendar.getTime();
            } else if (option == 3) {
                Calendar calendar = Calendar.getInstance();
                calendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH) - 2);
                calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
                calendar.set(Calendar.HOUR_OF_DAY, 23);
                calendar.set(Calendar.MINUTE, 59);
                calendar.set(Calendar.SECOND, 59);
                calendar.set(Calendar.MILLISECOND, 999);
                to = calendar.getTime();
            }
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(to);
            calendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH) - 5);
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            Date from = calendar.getTime();

            List<Fixture> analyzedFixtures = mService.getAnalystFixtureCompleted(analystIds, curUser.getAllowedCompetitions(), from, to);
            List<Fixture> selectedFixtures = new ArrayList<>();
            if (analystId != null) {
                User analyst = mService.getAnalyst(analystId);
                model.addAttribute("mAnalyst", analyst);
            }
            if (!analyzedFixtures.isEmpty()) {
                Date validFrom = null, validTo = null;
                switch (option) {
                    case 1:
                        calendar = Calendar.getInstance();
                        calendar.set(Calendar.DAY_OF_MONTH, 1);
                        validFrom = DateUtils.truncate(calendar.getTime(), Calendar.DATE);
                        validTo = new Date();
                        break;
                    case 2:
                        calendar = Calendar.getInstance();
                        calendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH) - 1);
                        calendar.set(Calendar.DAY_OF_MONTH, 1);
                        validFrom = DateUtils.truncate(calendar.getTime(), Calendar.DATE);
                        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
                        calendar.set(Calendar.HOUR_OF_DAY, 23);
                        calendar.set(Calendar.MINUTE, 59);
                        calendar.set(Calendar.SECOND, 59);
                        calendar.set(Calendar.MILLISECOND, 999);
                        validTo = calendar.getTime();
                        break;
                    case 3:
                        calendar = Calendar.getInstance();
                        calendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH) - 2);
                        calendar.set(Calendar.DAY_OF_MONTH, 1);
                        validFrom = DateUtils.truncate(calendar.getTime(), Calendar.DATE);
                        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
                        calendar.set(Calendar.HOUR_OF_DAY, 23);
                        calendar.set(Calendar.MINUTE, 59);
                        calendar.set(Calendar.SECOND, 59);
                        calendar.set(Calendar.MILLISECOND, 999);
                        validTo = calendar.getTime();
                        break;
                }
                if (validFrom != null && validTo != null) {
                    for (Fixture fixture : analyzedFixtures) {
                        if (fixture.getPublicationDate() != null) {
                            if (fixture.getPublicationDate().after(validFrom) && fixture.getPublicationDate().before(validTo)) {
                                selectedFixtures.add(fixture);
                            }
                        }
                    }
                }

//                ManagerGroupset groupset = curUser.getGroupset();
//                if (groupsetId != null) {
//                    groupset = mService.getManagerGroupset(groupsetId);
//                }
                model.addAttribute("mTotalGames", selectedFixtures.size());
                List<ManagerGroupset> managerGroupsets = mService.getManagerGroupsets();
                Map<Long, ManagerGroupset> groupsets = new HashMap<>();
                for (ManagerGroupset managerGroupset : managerGroupsets) {
                    groupsets.put(managerGroupset.getId(), managerGroupset);
                    for (ManagerGroupset userGroupset : curUser.getExtraGroupsets()) {
                        if (Long.compare(managerGroupset.getId(), userGroupset.getId()) == 0) {
                            managerGroupset.setHideSalary(BooleanUtils.isTrue(userGroupset.getHideSalary()));
                        }
                    }
                }
                AnalystHelper.setSalary(analyzedFixtures, selectedFixtures, groupsets);
                double totalSalary = 0, totalPenaltyQuality = 0, totalPenaltyQuantity = 0, totalBonus = 0;
                for (Fixture fixture : selectedFixtures) {
                    if (fixture.getSalary() != null) {
                        totalSalary += fixture.getSalary();
                    }
                    if (fixture.getQualityPenaltyValue() != null) {
                        totalPenaltyQuality += fixture.getQualityPenaltyValue();
                    }
                    if (fixture.getQuantityPenaltyValue() != null) {
                        totalPenaltyQuantity += fixture.getQuantityPenaltyValue();
                    }
                    if (fixture.getBonusValue() != null) {
                        totalBonus += fixture.getBonusValue();
                    }
                }

                model.addAttribute("mTotalSalary", Math.round(totalSalary * 100D) / 100D);
                model.addAttribute("mTotalPenaltyQuality", Math.round(totalPenaltyQuality * 100D) / 100D);
                model.addAttribute("mTotalPenaltyQuantity", Math.round(totalPenaltyQuantity * 100D) / 100D);
                model.addAttribute("mTotalBonus", Math.round(totalBonus * 100D) / 100D);
            }
            model.addAttribute("mGames", selectedFixtures);

            Map<Long, Integer> gamesDurationMap = new HashMap<>();
            if (!selectedFixtures.isEmpty()) {
                for (Fixture fixture : selectedFixtures) {
                    gamesDurationMap.put(fixture.getId(), AnalystHelper.getEstimatedDuration(fixture));
                }
            }
            model.addAttribute("mGamesDuration", gamesDurationMap);

            Map<Long, AnalystDataWrapper> analystTotals = new HashMap<>();
            if (!selectedFixtures.isEmpty()) {
                for (Fixture fixture : selectedFixtures) {
                    if (fixture.getAnalystId() != null) {
                        if (!analystTotals.containsKey(fixture.getAnalystId())) {
                            AnalystDataWrapper wrapper = new AnalystDataWrapper();
                            wrapper.setFixtures(new ArrayList<Fixture>());
                            wrapper.setSalary(0D);
                            wrapper.setQualityPenalty(0D);
                            wrapper.setQuantityPenalty(0D);
                            wrapper.setBonus(0D);
                            wrapper.setAnalyst(fixture.getAnalyst());
                            analystTotals.put(fixture.getAnalystId(), wrapper);
                        }

                        AnalystDataWrapper wrapper = analystTotals.get(fixture.getAnalystId());
                        wrapper.getFixtures().add(fixture);
                        wrapper.setSalary(Math.round((wrapper.getSalary() + fixture.getSalary()) * 100D) / 100D);
                        wrapper.setQualityPenalty(Math.round((wrapper.getQualityPenalty() + (fixture.getQualityPenaltyValue() != null ? fixture.getQualityPenaltyValue() : 0)) * 100D) / 100D);
                        wrapper.setQuantityPenalty(Math.round((wrapper.getQuantityPenalty() + (fixture.getQuantityPenaltyValue() != null ? fixture.getQuantityPenaltyValue() : 0)) * 100D) / 100D);
                        wrapper.setBonus(Math.round((wrapper.getBonus() + (fixture.getBonusValue() != null ? fixture.getBonusValue() : 0)) * 100D) / 100D);
                    }
                }
            }
            List<AnalystDataWrapper> analystTotalList = new ArrayList<>(analystTotals.values());
            Collections.sort(analystTotalList, new Comparator<AnalystDataWrapper>() {
                @Override
                public int compare(AnalystDataWrapper o1, AnalystDataWrapper o2) {
                    return o1.getAnalyst().compareTo(o2.getAnalyst());
                }
            });
            model.addAttribute("mAnalystTotals", analystTotalList);

            return pageGamesReportData;
        } else {
            return "";
        }
    }

    public void sendAnalystMail(User curUser, List<User> analysts, Long analystId, Long fixtureId, Integer type) {
        Fixture fixture = null;
        List<Fixture> fixtures = CacheInterface.getCache(curUser.getId()).getFixtures();
        if (fixtures != null) {
            for (Fixture tmpFixture : fixtures) {
                if (Long.compare(tmpFixture.getId(), fixtureId) == 0) {
                    fixture = tmpFixture;
                    break;
                }
            }

            if (fixture != null) {
                User analyst = null;
                for (User tmpAnalyst : analysts) {
                    if (Long.compare(tmpAnalyst.getId(), analystId) == 0) {
                        analyst = tmpAnalyst;
                        break;
                    }
                }

                if (analyst != null) {
                    String to = analyst.getEmail();
                    String subject = "", body = "";
                    if (StringUtils.equalsIgnoreCase(analyst.getLanguage(), "it")) {
                        if (type == 1) {
                            subject = "[ASSEGNATA] ";

                            body = "Gentile <b>" + analyst.getFirstName() + " " + analyst.getLastName() + "</b>,<br>\n"
                                    + "<br>\n"
                                    + "ti è stata assegnata una nuova partita. Di seguito puoi trovare i dettagli <br>\n"
                                    + "<br>\n"
                                    + "Partita: <b>" + fixture.getHomeTeam() + "-" + fixture.getAwayTeam() + " (" + DateHelper.toStringAnalystMail(fixture.getDate()) + ")</b><br>\n"
                                    + "Competizione: <b>" + fixture.getCompetitionName() + (Long.compare(fixture.getCountryId(), 0) != 0 ? " (" + fixture.getCountryName().toUpperCase() + ")" : "") + "</b><br>\n"
                                    + "Giornata: <b>" + fixture.getMatchday() + "</b> <br>\n"
                                    + "Video Disponibile: <b>" + (fixture.getVideoName() == null ? "NO" : "YES") + "</b><br>\n";
                            if (fixture.getVideoName() == null) {
                                body += "<b> Riceverai una nuova mail quando il video sarà disponibile </b>";
                            }
                        } else if (type == 2) {
                            subject = "[ASSEGNAZIONE RIMOSSA] ";

                            body = "Gentile <b>" + analyst.getFirstName() + " " + analyst.getLastName() + "</b>,<br>\n"
                                    + "<br>\n"
                                    + "la seguente partita è stata rimossa dal tuo archivio.<br>La partita non dev'essere più analizzata\n"
                                    + "<br>\n"
                                    + "Partita: <b>" + fixture.getHomeTeam() + "-" + fixture.getAwayTeam() + " (" + DateHelper.toStringAnalystMail(fixture.getDate()) + ")</b><br>\n"
                                    + "Competizione: <b>" + fixture.getCompetitionName() + (Long.compare(fixture.getCountryId(), 0) != 0 ? " (" + fixture.getCountryName().toUpperCase() + ")" : "") + "</b><br>\n"
                                    + "Giornata: <b>" + fixture.getMatchday() + "</b> <br>";
                        }
                    } else {
                        if (type == 1) {
                            subject = "[ASSIGNED] ";

                            body = "Dear <b>" + analyst.getFirstName() + " " + analyst.getLastName() + "</b>,<br>\n"
                                    + "<br>\n"
                                    + "a new match has been assigned to you. Below you can find the details <br>\n"
                                    + "<br>\n"
                                    + "Match: <b>" + fixture.getHomeTeam() + "-" + fixture.getAwayTeam() + " (" + DateHelper.toStringAnalystMail(fixture.getDate()) + ")</b><br>\n"
                                    + "Competition: <b>" + fixture.getCompetitionName() + (Long.compare(fixture.getCountryId(), 0) != 0 ? " (" + fixture.getCountryName().toUpperCase() + ")" : "") + "</b><br>\n"
                                    + "Match Day: <b>" + fixture.getMatchday() + "</b> <br>\n"
                                    + "Video Available: <b>" + (fixture.getVideoName() == null ? "NO" : "YES") + "</b><br>\n";
                            if (fixture.getVideoName() == null) {
                                body += "<b> You will receive a new mail when video will be available </b>";
                            }
                        } else if (type == 2) {
                            subject = "[ASSIGNMENT REMOVED] ";

                            body = "Dear <b>" + analyst.getFirstName() + " " + analyst.getLastName() + "</b>,<br>\n"
                                    + "<br>\n"
                                    + "the following match has been removed from your archive.<br>You are not requested anymore to analyze the match\n"
                                    + "<br>\n"
                                    + "Match: <b>" + fixture.getHomeTeam() + "-" + fixture.getAwayTeam() + " (" + DateHelper.toStringAnalystMail(fixture.getDate()) + ")</b><br>\n"
                                    + "Competition: <b>" + fixture.getCompetitionName() + (Long.compare(fixture.getCountryId(), 0) != 0 ? " (" + fixture.getCountryName().toUpperCase() + ")" : "") + "</b><br>\n"
                                    + "Match Day: <b>" + fixture.getMatchday() + "</b> <br>";
                        }
                    }
                    subject += fixture.getHomeTeam() + "-" + fixture.getAwayTeam() + " "
                            + DateHelper.toStringAnalystMail(fixture.getDate()) + " - "
                            + analyst.getFirstName() + " " + analyst.getLastName();

                    MailHelper mail = new MailHelper(SpringApplicationContextHelper.getMessage("email.smtp"), SpringApplicationContextHelper.getMessage("email.smtp.port"), SpringApplicationContextHelper.getMessage("email.smtp.user"), SpringApplicationContextHelper.getMessage("email.smtp.pwd"));
                    mail.sendMail(SpringApplicationContextHelper.getMessage("email.from"), to, null, "<EMAIL>", null, null, subject, body, null, "");
                } else {
                    log.error("Analyst not found in analyst map. User is " + curUser.getId() + ", analystId is " + analystId + ", analyst list size is: " + analysts.size());
                }
            } else {
                log.error("Fixture not found in user map. User is " + curUser.getId() + ", fixtureId is " + fixtureId + ", fixture map size is: " + fixtures.size());
            }
        }
    }

    @RequestMapping("/orders")
    public String orders(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response) {
        if (!this.initModule(session, model)) {
            return pageRedirect(pageHome);
        }
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        if (!curUser.hasAccess(4)) {
            return pageRedirect(pageHome);
        }

        List<Order> orders = mService.getOrders();
        model.addAttribute("mOrders", orders);
        List<VtigerField> originValues = mService.getVtigerOriginValues();
        model.addAttribute("mOriginValues", originValues);
        CacheInterface.updateOrders(curUser.getId(), orders);

        return pageOrders;
    }

    @RequestMapping("/getOrderDetails")
    public @ResponseBody
    String getOrderDetails(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam("orderId") Long orderId) {
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        if (!curUser.hasAccess(4)) {
            return "permissionDenied";
        }

        Order order = null;
        List<Order> orders = CacheInterface.getCache(curUser.getId()).getOrders();
        if (orders != null && !orders.isEmpty()) {
            for (Order tmpOrder : orders) {
                if (tmpOrder.getOrderId() != null && Long.compare(tmpOrder.getOrderId(), orderId) == 0) {
                    order = tmpOrder;
                    break;
                }
            }
        }

        String ajaxReponse;
        if (order != null) {
            String found = "";
            List<VtigerContact> contacts = mService.getContactsByEmail(order.getEmail());
            if (contacts == null || contacts.isEmpty()) {
                contacts = mService.getContactsByPhone(order.getPhoneNumber());
                if (contacts == null || contacts.isEmpty()) {
                    contacts = mService.getContactsByName(order.getFirstName(), order.getLastName());
                    if (contacts == null) {
                        contacts = new ArrayList<>();
                    } else {
                        if (!contacts.isEmpty()) {
                            found = "name";
                        }
                    }
                } else {
                    found = "phone";
                }
            } else {
                found = "email";
            }

            Gson gson = new GsonBuilder()
                    .setDateFormat("dd/MM/yyyy").create();
            JsonObject jsonObject = new JsonObject();
            jsonObject.addProperty("found", found);
            jsonObject.add("contacts", gson.toJsonTree(contacts));
            jsonObject.add("order", gson.toJsonTree(order));
            ajaxReponse = gson.toJson(jsonObject);
        } else {
            Gson gson = new Gson();
            JsonObject jsonObject = new JsonObject();
            jsonObject.addProperty("found", "not found");
            ajaxReponse = gson.toJson(jsonObject);
        }

        return ajaxReponse;
    }

    @RequestMapping("/saveContact")
    public @ResponseBody
    String saveContact(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam("orderId") Long orderId, @RequestParam("contactId") Long contactId, @RequestParam("firstName") String firstName,
            @RequestParam("lastName") String lastName, @RequestParam("email") String email, @RequestParam("phoneNumber") String phoneNumber,
            @RequestParam("birthDay") String birthDay, @RequestParam("country") String country, @RequestParam("province") String province,
            @RequestParam("cap") String cap, @RequestParam("city") String city, @RequestParam("street") String street,
            @RequestParam("origin") String origin, @RequestParam("tin") String tin, @RequestParam("vatNumber") String vatNumber,
            @RequestParam("action") String action) {
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        if (!curUser.hasAccess(4)) {
            return "permissionDenied";
        }

        String status = "ko";
        try {
            SimpleDateFormat formatter = new SimpleDateFormat("dd/MM/yyyy");
            Date birthDayDate = null;
            if (StringUtils.isNotBlank(birthDay)) {
                try {
                    birthDayDate = formatter.parse(birthDay);
                } catch (ParseException ex) {
                    GlobalHelper.reportError(ex);
                }
            }

            URL url = new URL("https://server.sics.it/vtigercrm/modules/CMS/actions/ExternalContactCreation.php");
            // Apertura della connessione HTTP
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            // Impostazione del metodo di richiesta come POST
            connection.setRequestMethod("POST");
            // Attivazione dell'invio di dati
            connection.setDoOutput(true);
            connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            // Creazione dei parametri da inviare
            String parametri = "firstName=" + firstName;
            parametri += "&lastName=" + lastName;
            parametri += "&email=" + email;
            parametri += "&phoneNumber=" + phoneNumber;
            parametri += "&birthDay=" + (birthDayDate == null ? "" : DateHelper.toStringExtDatabaseVtiger(birthDayDate));
            parametri += "&country=" + country;
            parametri += "&province=" + province;
            parametri += "&cap=" + cap;
            parametri += "&city=" + city;
            parametri += "&street=" + street;
            parametri += "&origin=" + origin.replaceAll(",", " |##| ");
            parametri += "&tin=" + tin;
            parametri += "&vatNumber=" + vatNumber;
            parametri += "&action=" + action;
            parametri += "&contactId=" + contactId;

            try (OutputStream os = connection.getOutputStream(); BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(os, "UTF-8"))) {
                // Scrittura dei parametri nell'output stream
                writer.write(parametri);
                writer.flush();
            }

            StringBuilder requestResponse;
            try (BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()))) {
                String inputLine;
                requestResponse = new StringBuilder();
                while ((inputLine = in.readLine()) != null) {
                    requestResponse.append(inputLine);
                }
            }

            status = requestResponse.toString();
            connection.disconnect();

            if (status.equalsIgnoreCase("Done")) {
                mService.updateOrderStatus(orderId, "Completed");
            }
        } catch (IOException ex) {
            GlobalHelper.reportError(ex);
        }

        return status;
    }

    @RequestMapping("/confirmOrder")
    public @ResponseBody
    String confirmOrder(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam("orderId") Long orderId) {
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        if (!curUser.hasAccess(4)) {
            return "permissionDenied";
        }

        mService.updateOrderStatus(orderId, "Completed");
        return "Done";
    }

    @RequestMapping("/vtigerErrors")
    public String vtigerErrors(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response) {
        if (!this.initModule(session, model)) {
            return pageRedirect(pageHome);
        }
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        if (!curUser.hasAccess(5)) {
            return pageRedirect(pageHome);
        }

        List<VtigerError> errors = mService.getVtigerErrors();
        model.addAttribute("mErrors", errors);

        return pageVtigerErrors;
    }

    @RequestMapping("/reimportFixture")
    public @ResponseBody
    String reimportFixture(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam("fixtureId") Long fixtureId) {
        if (!this.initModule(session, model)) {
            return "";
        }
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        if (!curUser.hasAccess(1)) {
            return "";
        }

        if (fixtureId != null) {
            Long isReloadable = mService.isFixtureReimportable(fixtureId);
            if (isReloadable != null) {
                mService.reimportFixture(fixtureId);
                return "ok";
            }
        }

        return "ko";
    }

    @RequestMapping("/groupset")
    public String groupset(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response) {
        if (!this.initModule(session, model)) {
            return pageRedirect(pageHome);
        }
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        if (!curUser.hasAccess(6)) {
            return pageRedirect(pageHome);
        }

        List<Groupset> groupset = mService.getGroupsets();
        Collections.sort(groupset, new Comparator<Groupset>() {
            @Override
            public int compare(Groupset o1, Groupset o2) {
                Boolean o1Check = o1.getIsEditable();
                Boolean o2Check = o2.getIsEditable();
                if (Boolean.compare(o1Check, o2Check) == 0) {
                    return o1.getName().compareTo(o2.getName());
                } else {
                    return o2Check.compareTo(o1Check);
                }
            }
        });
        model.addAttribute("mGroupsets", groupset);
        List<Competition> allCompetitions = mService.getAllCompetitions();
        model.addAttribute("mCompetitions", allCompetitions);
        List<Competition> allCompetitionsDa = mService.getAllCompetitionsDataAccess();
        model.addAttribute("mCompetitionsDa", allCompetitionsDa);
        List<Competition> allTacticalCompetitions = mService.getAllTacticalCompetitions();
        List<Competition> completeTacticalCompetitions = new ArrayList<>();
        for (Competition tactical : allTacticalCompetitions) {
            for (Competition competition : allCompetitions) {
                if (Long.compare(tactical.getId(), competition.getId()) == 0) {
                    completeTacticalCompetitions.add(competition);
                    break;
                }
            }
        }
        model.addAttribute("mTacticalCompetitions", completeTacticalCompetitions);

        // carico anche i groupset del manager
        List<ManagerGroupset> managerGroupset = mService.getManagerGroupsets();
        model.addAttribute("mManagerGroupsets", managerGroupset);
        // mi serve anche la lista degli utenti del manager per assegnare i gruppi
        List<User> managerUsers = mService.getUsers();
        model.addAttribute("mManagerUsers", managerUsers);

        return pageGroupset;
    }

    @RequestMapping("/createGroupset")
    public @ResponseBody
    String createGroupset(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam("name") String name, @RequestParam("maxDownload") Integer maxDownload, @RequestParam("maxDownloadReport") Integer maxDownloadReport,
            @RequestParam("cloudSize") Integer cloudSize, @RequestParam("exportTimeLimit") Long exportTimeLimit, @RequestParam("competitionIds") String competitionIds,
            @RequestParam("competitionIdsVm") String competitionIdsVm, @RequestParam("competitionIdsDa") String competitionIdsDa,
            @RequestParam("tactCompetitionIds") String tactCompetitionIds, @RequestParam("guest") Boolean guest) {
        if (!this.initModule(session, model)) {
            return "";
        }
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        if (!curUser.hasAccess(6)) {
            return "";
        }

        if (StringUtils.isNotBlank(name) && maxDownload != null && maxDownloadReport != null && cloudSize != null && exportTimeLimit != null
                && (StringUtils.isNotBlank(competitionIds) || StringUtils.isNotBlank(competitionIdsVm) || StringUtils.isNotBlank(competitionIdsDa) || StringUtils.isNotBlank(tactCompetitionIds))) {
            // se tutto valido allora aggiorno tutto
            Groupset alreadyExists = mService.getGroupsetByName(name);
            if (alreadyExists != null) {
                return "exists";
            }

            Groupset groupset = new Groupset();
            groupset.setName(name);
            groupset.setMaxDownload(maxDownload);
            groupset.setMaxDownloadReport(maxDownloadReport);
            groupset.setCloudSize(cloudSize);
            groupset.setExportTimeLimit(exportTimeLimit);
            groupset.setCompetitions(competitionIds);
            groupset.setCompetitionsVm(competitionIdsVm);
            groupset.setCompetitionsDa(competitionIdsDa);
            groupset.setTactCompetitions(tactCompetitionIds);
            groupset.setGuest(BooleanUtils.isTrue(guest) ? 1 : 0);

            groupset.setId(mService.createGroupset(groupset));
            GlobalHelper.writeLogData(session, GlobalHelper.kActionGroupsetCreate, curUser.getEmail(), curUser.getPassword(), curUser.getId(), "ID:" + groupset.getId());
            if (!SSHHelper.createGroupsetFolders(groupset.getId())) {
                String to = "<EMAIL>";
                String subject, body;

                body = "Scripts /opt/scripts/create-groupset.sh was not able to create the folders for the group " + groupset.getId() + ".";
                subject = "[SICS Manager - Groupset] Folder(s) not created successfully";

                MailHelper mail = new MailHelper(SpringApplicationContextHelper.getMessage("email.smtp"), SpringApplicationContextHelper.getMessage("email.smtp.port"), SpringApplicationContextHelper.getMessage("email.smtp.user"), SpringApplicationContextHelper.getMessage("email.smtp.pwd"));
                mail.sendMail(SpringApplicationContextHelper.getMessage("email.from"), to, null, "<EMAIL>", null, null, subject, body, null, "");
            }
            if (StringUtils.isNotBlank(competitionIds)) {
                mService.createGroupsetCompetition(groupset);
            }
            if (StringUtils.isNotBlank(competitionIdsVm)) {
                mService.createGroupsetCompetitionVm(groupset);
            }
            if (StringUtils.isNotBlank(competitionIdsDa)) {
                mService.createGroupsetCompetitionDa(groupset);
            }
            if (StringUtils.isNotBlank(tactCompetitionIds)) {
                mService.createGroupsetCompetitionTactical(groupset);
            }

            return "ok";
        }

        return "ko";
    }

    @RequestMapping("/updateGroupset")
    public @ResponseBody
    String updateGroupset(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam("groupsetId") Long groupsetId, @RequestParam("name") String name, @RequestParam("maxDownload") Integer maxDownload,
            @RequestParam("maxDownloadReport") Integer maxDownloadReport, @RequestParam("cloudSize") Integer cloudSize, @RequestParam("exportTimeLimit") Long exportTimeLimit,
            @RequestParam("competitionIds") String competitionIds, @RequestParam("competitionIdsVm") String competitionIdsVm, @RequestParam("competitionIdsDa") String competitionIdsDa,
            @RequestParam("tactCompetitionIds") String tactCompetitionIds, @RequestParam("guest") Boolean guest) {
        if (!this.initModule(session, model)) {
            return "";
        }
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        if (!curUser.hasAccess(6)) {
            return "";
        }

        if (groupsetId != null) {
            if (StringUtils.isNotBlank(name) && maxDownload != null && maxDownloadReport != null && cloudSize != null && exportTimeLimit != null
                    && (StringUtils.isNotBlank(competitionIds) || StringUtils.isNotBlank(competitionIdsVm) || StringUtils.isNotBlank(competitionIdsDa) || StringUtils.isNotBlank(tactCompetitionIds))) {
                // se tutto valido allora aggiorno tutto
                Groupset groupset = mService.getGroupset(groupsetId);
                if (groupset != null) {
//                    if (BooleanUtils.isFalse(groupset.getIsEditable())) {
//                        return "noneditable";
//                    }
                    Groupset alreadyExists = mService.getGroupsetByName(name);
                    if (alreadyExists != null && Long.compare(alreadyExists.getId(), groupsetId) != 0) {
                        return "exists";
                    }

                    groupset.setName(name);
                    groupset.setMaxDownload(maxDownload);
                    groupset.setMaxDownloadReport(maxDownloadReport);
                    groupset.setCloudSize(cloudSize);
                    groupset.setExportTimeLimit(exportTimeLimit);
                    groupset.setCompetitions(competitionIds);
                    groupset.setCompetitionsVm(competitionIdsVm);
                    groupset.setCompetitionsDa(competitionIdsDa);
                    groupset.setTactCompetitions(tactCompetitionIds);
                    groupset.setGuest(BooleanUtils.isTrue(guest) ? 1 : 0);

                    mService.updateGroupset(groupset);
                    GlobalHelper.writeLogData(session, GlobalHelper.kActionGroupsetUpdate, curUser.getEmail(), curUser.getPassword(), curUser.getId(), "ID:" + groupset.getId());
                    mService.deleteGroupsetCompetition(groupsetId);     // tolgo di base tutte le competizioni
                    if (StringUtils.isNotBlank(competitionIds)) {
                        mService.createGroupsetCompetition(groupset);
                    }
                    mService.deleteGroupsetCompetitionVm(groupsetId);   // tolgo di base tutte le competizioni
                    if (StringUtils.isNotBlank(competitionIdsVm)) {
                        mService.createGroupsetCompetitionVm(groupset);
                    }
                    mService.deleteGroupsetCompetitionDa(groupsetId);   // tolgo di base tutte le competizioni
                    if (StringUtils.isNotBlank(competitionIdsDa)) {
                        mService.createGroupsetCompetitionDa(groupset);
                    }
                    mService.deleteGroupsetCompetitionTactical(groupsetId);   // tolgo di base tutte le competizioni
                    if (StringUtils.isNotBlank(tactCompetitionIds)) {
                        // controllo se c'è il groupset_level
                        Long hasLevelSix = mService.hasGroupsetLevel(groupsetId, 6L);
                        if (hasLevelSix == null) {
                            mService.createSingleGroupsetLevel(groupsetId, 6L);
                        }

                        mService.createGroupsetCompetitionTactical(groupset);
                    }

                    return "ok";
                }
            }
        }

        return "ko";
    }

    @RequestMapping("/createManagerGroupset")
    public @ResponseBody
    String createManagerGroupset(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam("name") String name, @RequestParam("salary40") Double salary40, @RequestParam("salary60") Double salary60, @RequestParam("salary80") Double salary80,
            @RequestParam("salary90") Double salary90, @RequestParam("salary120") Double salary120, @RequestParam("hideSalary") Boolean hideSalary,
            @RequestParam("competitionIds") String competitionIds, @RequestParam("userIds") String userIds) {
        if (!this.initModule(session, model)) {
            return "";
        }
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        if (!curUser.hasAccess(6)) {
            return "";
        }

        if (StringUtils.isNotBlank(name) && salary40 != null && salary60 != null && salary80 != null && salary90 != null && salary120 != null && StringUtils.isNotBlank(competitionIds)) {
            // se tutto valido allora aggiorno tutto
            ManagerGroupset alreadyExists = mService.getManagerGroupsetByName(name);
            if (alreadyExists != null) {
                return "exists";
            }

            ManagerGroupset groupset = new ManagerGroupset();
            groupset.setName(name);
            groupset.setSalary40(salary40);
            groupset.setSalary60(salary60);
            groupset.setSalary80(salary80);
            groupset.setSalary90(salary90);
            groupset.setSalary120(salary120);
            groupset.setHideSalary(hideSalary);
            groupset.setCompetitions(competitionIds);

            groupset.setId(mService.createManagerGroupset(groupset));
            GlobalHelper.writeLogData(session, GlobalHelper.kActionManagerGroupsetCreate, curUser.getEmail(), curUser.getPassword(), curUser.getId(), "ID:" + groupset.getId());
            mService.createManagerGroupsetCompetition(groupset);

            if (StringUtils.isNotBlank(userIds)) {
                mService.removeUsersGroupset(groupset.getId());
                mService.setUsersGroupset(groupset.getId(), userIds);
            }

            return "ok";
        }

        return "ko";
    }

    @RequestMapping("/updateManagerGroupset")
    public @ResponseBody
    String updateManagerGroupset(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam("groupsetId") Long groupsetId, @RequestParam("name") String name, @RequestParam("salary40") Double salary40,
            @RequestParam("salary60") Double salary60, @RequestParam("salary80") Double salary80, @RequestParam("salary90") Double salary90,
            @RequestParam("salary120") Double salary120, @RequestParam("hideSalary") Boolean hideSalary,
            @RequestParam("competitionIds") String competitionIds, @RequestParam("userIds") String userIds) {
        if (!this.initModule(session, model)) {
            return "";
        }
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        if (!curUser.hasAccess(6)) {
            return "";
        }

        if (groupsetId != null) {
            if (StringUtils.isNotBlank(name) && salary40 != null && salary60 != null && salary80 != null && salary90 != null && salary120 != null && StringUtils.isNotBlank(competitionIds)) {
                // se tutto valido allora aggiorno tutto
                ManagerGroupset groupset = mService.getManagerGroupset(groupsetId);
                if (groupset != null) {
                    ManagerGroupset alreadyExists = mService.getManagerGroupsetByName(name);
                    if (alreadyExists != null && Long.compare(alreadyExists.getId(), groupsetId) != 0) {
                        return "exists";
                    }

                    groupset.setName(name);
                    groupset.setSalary40(salary40);
                    groupset.setSalary60(salary60);
                    groupset.setSalary80(salary80);
                    groupset.setSalary90(salary90);
                    groupset.setSalary120(salary120);
                    groupset.setHideSalary(hideSalary);
                    groupset.setCompetitions(competitionIds);

                    mService.updateManagerGroupset(groupset);
                    GlobalHelper.writeLogData(session, GlobalHelper.kActionManagerGroupsetUpdate, curUser.getEmail(), curUser.getPassword(), curUser.getId(), "ID:" + groupset.getId());
                    mService.deleteManagerGroupsetCompetition(groupsetId);
                    mService.createManagerGroupsetCompetition(groupset);

                    mService.removeUsersGroupset(groupset.getId());
                    if (StringUtils.isNotBlank(userIds)) {
                        mService.setUsersGroupset(groupset.getId(), userIds);
                    }

                    // aggiorno il gruppo di tutti quelli collegati
                    try {
                        Map<Long, ArrayList<HttpSession>> sessions = SessionListener.getSessions();
                        for (Long userId : sessions.keySet()) {
                            for (HttpSession userSession : sessions.get(userId)) {
                                User user = (User) userSession.getAttribute(GlobalHelper.kBeanUtente);
                                if (user != null && Long.compare(user.getGroupsetId(), groupsetId) == 0) {
                                    // se ho trovato un utente del gruppo che sto aggiornando allora sistemo i dati
                                    user.setGroupset(groupset);
                                }
                            }
                        }
                    } catch (Exception ex) {
                        GlobalHelper.reportError(ex);
                    }

                    return "ok";
                }
            }
        }

        return "ko";
    }

    @RequestMapping("/groupsetChecker")
    public String groupsetChecker(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam(value = "groupsetId", required = false) Long groupsetId) {
        if (!this.initModule(session, model)) {
            return pageRedirect(pageHome);
        }
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        if (!curUser.hasAccess(6)) {
            return pageRedirect(pageHome);
        }

        List<Groupset> groupsets = mService.getGroupsetForChecker();
        model.addAttribute("mGroupsets", groupsets);
        model.addAttribute("mGroupset", groupsetId);
        List<GroupsetChecker> groupsetData = mService.getGroupsetCheckerData(groupsetId);
        Collections.sort(groupsetData, new Comparator<GroupsetChecker>() {
            @Override
            public int compare(GroupsetChecker o1, GroupsetChecker o2) {
                Boolean o1Check = o1.hasOnlySicsTv();
                Boolean o2Check = o2.hasOnlySicsTv();
                if (Boolean.compare(o1Check, o2Check) == 0) {
                    Integer o1Size = o1.getData() != null ? o1.getData().size() : 0;
                    Integer o2Size = o2.getData() != null ? o2.getData().size() : 0;
                    return o2Size.compareTo(o1Size);
                } else {
                    return o1Check.compareTo(o2Check);
                }
            }
        });

        model.addAttribute("mGroupsetData", groupsetData);

        return pageGroupsetChecker;
    }

    @RequestMapping("/vtigerLink")
    public @ResponseBody
    String vtigerLink(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam("firstName") String firstName, @RequestParam("lastName") String lastName, @RequestParam("email") String email) {
        if (!this.initModule(session, model)) {
            return "";
        }
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        if (!curUser.hasAccess(6)) { // per ora va bene 6, viene usato su groupset
            return "";
        }

        if (StringUtils.isNotBlank(firstName)
                && StringUtils.isNotBlank(lastName)
                && StringUtils.isNotBlank(email)) {
            Long contactId = mService.getVtigerContactId(firstName, lastName, email);
            if (contactId != null) {
                // aggiunta modifica dentro a /var/www/html/vtigercrm/includes/http/Request.php, cerca per "MARCO ZEN"
                return "https://server.sics.it/vtigercrm/index.php?module=Contacts&view=Detail&record=" + contactId + "&app=&_csrf_token=690d8fa781cf0a75c433d2120efbd1e379317519";
            }
        }

        return "ko";
    }

    @RequestMapping("/logChecker")
    public String logChecker(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response) {
        if (!this.initModule(session, model)) {
            return pageRedirect(pageHome);
        }
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        if (!curUser.hasAccess(7)) {
            return pageRedirect(pageHome);
        }

        List<User> userList = mService.getLogdataUsers();
        model.addAttribute("mUsers", userList);
        Map<Long, Groupset> groupsetList = new HashMap<>();
        for (User user : userList) {
            if (user.getGroupsetId() != null) {
                if (!groupsetList.containsKey(user.getGroupsetId())) {
                    Groupset groupset = new Groupset();
                    groupset.setId(user.getGroupsetId());
                    groupset.setName(user.getGroupsetName());
                    groupsetList.put(user.getGroupsetId(), groupset);
                }
            }
        }
        List<Groupset> groupsets = new ArrayList<>(groupsetList.values());
        Collections.sort(groupsets, new Comparator<Groupset>() {
            @Override
            public int compare(Groupset o1, Groupset o2) {
                return o1.getName().compareTo(o2.getName());
            }
        });
        model.addAttribute("mGroupsets", groupsets);

        model.addAttribute("mMinDate", DateHelper.toStringUsaExt(DateUtils.addYears(new Date(), -1)));
        model.addAttribute("mMaxDate", DateHelper.toStringUsaExt(new Date()));

        return pageLogChecker;
    }

    @RequestMapping("/logCheckerData")
    public String logCheckerData(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam(value = "userId", required = false) Long userId, @RequestParam(value = "groupsetId", required = false) Long groupsetId,
            @RequestParam("from") String fromDate, @RequestParam("to") String toDate, @RequestParam("groupType") Integer groupType) {
        if (!this.initModule(session, model)) {
            return "";
        }
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        if (!curUser.hasAccess(7)) {
            return "";
        }

        try {
            if ((userId != null || groupsetId != null) && StringUtils.isNotBlank(fromDate) && StringUtils.isNotBlank(toDate) && groupType != null) {
                if (userId != null) {
                    User tvUser = mService.getTvUser(userId);
                    model.addAttribute("mTvUser", tvUser);

                    TvUserLimits limits = mService.getTvUserLimits(userId);
                    if (limits != null) {
                        model.addAttribute("mTvUserLimits", limits);
                    }
                } else if (groupsetId != null) {
                    TvUserLimits limits = mService.getTvGroupsetLimits(groupsetId);
                    if (limits != null) {
                        model.addAttribute("mTvGroupsetLimits", limits);
                    }
                    List<User> groupsetUsers = mService.getGroupsetUserList(groupsetId);
                    model.addAttribute("mTvGroupsetUsers", groupsetUsers);
                    if (groupsetUsers != null && !groupsetUsers.isEmpty()) {
                        Groupset groupset = new Groupset();
                        groupset.setId(groupsetUsers.get(0).getGroupsetId());
                        groupset.setName(groupsetUsers.get(0).getGroupsetName());
                        model.addAttribute("mGroupset", groupset);
                    }
                }

                SimpleDateFormat format = new SimpleDateFormat("MM/dd/yyyy");
                Date from = format.parse(fromDate);
                Date to = format.parse(toDate);
                int queryGroupType = 0;
                switch (groupType) {
                    case 1:
                        queryGroupType = 1;
                        break;
                    case 2:
                    case 3:
                    case 4:
                        queryGroupType = 2;
                        break;
                    case 5:
                        queryGroupType = 3;
                        break;
                    default:
                        break;
                }
                model.addAttribute("mGroupType", queryGroupType);
                List<LogData> userActionsGrouped = mService.getUserActionsByParams(userId, groupsetId, queryGroupType, from, to);
                Map<Date, Map<String, Integer>> groupedData = new LinkedHashMap<>();

                List<String> xValues = new ArrayList<>();
                List<Integer> totalValues = new ArrayList<>();
                List<Integer> loginValues = new ArrayList<>();
                List<Integer> playlistValues = new ArrayList<>();
                List<Integer> watchlistValues = new ArrayList<>();
                List<Integer> downloadValues = new ArrayList<>();
                List<Integer> streamValues = new ArrayList<>();
                List<Integer> xmlJsonValues = new ArrayList<>();
                List<Integer> exportValues = new ArrayList<>();

                List<Integer> tableFirstColumnValues = new ArrayList<>();
                // raggruppo tutti i dati per mese
                // Map<Mese, Map<Action, Amount>>
                Calendar calendar = Calendar.getInstance();
                for (LogData data : userActionsGrouped) {
                    switch (groupType) {
                        case 1: {
                            Date logDate = data.getDate();
                            calendar.setTime(logDate);
                            calendar.set(Calendar.DAY_OF_WEEK, 1);
                            String month = calendar.getDisplayName(Calendar.MONTH, Calendar.SHORT, Locale.ENGLISH);
                            int year = calendar.get(Calendar.YEAR);
                            String xValue = "Week " + calendar.get(Calendar.WEEK_OF_YEAR) + " " + year + " (" + month + ")";
                            if (!xValues.contains(xValue)) {
                                tableFirstColumnValues.add(calendar.get(Calendar.WEEK_OF_YEAR));
                                xValues.add(xValue);
                            }
                            groupedData.putIfAbsent(calendar.getTime(), new HashMap<String, Integer>());
                            groupedData.get(calendar.getTime()).put(data.getAction(), data.getAmount());
                            break;
                        }
                        case 2: {
                            Date logDate = data.getDate();
                            calendar.setTime(logDate);
                            calendar.set(Calendar.DAY_OF_MONTH, 1);
                            String month = calendar.getDisplayName(Calendar.MONTH, Calendar.LONG, Locale.ENGLISH);
                            int year = calendar.get(Calendar.YEAR);
                            String xValue = month + " " + year;
                            if (!xValues.contains(xValue)) {
                                tableFirstColumnValues.add(calendar.get(Calendar.MONTH) + 1);
                                xValues.add(xValue);
                            }
                            groupedData.putIfAbsent(calendar.getTime(), new HashMap<String, Integer>());
                            groupedData.get(calendar.getTime()).put(data.getAction(), data.getAmount());
                            break;
                        }
                        case 3: {
                            // TO FIX
                            Date logDate = data.getDate();
                            calendar.setTime(logDate);

                            int monthNumber = calendar.get(Calendar.MONTH) + 1; // gennaio è lo 0
                            int correctMonth = (int) (Math.floor(monthNumber / 3) * 3) + 1;

                            calendar.set(Calendar.MONTH, correctMonth);
                            String startMonth = calendar.getDisplayName(Calendar.MONTH, Calendar.SHORT, Locale.ENGLISH);
                            calendar.set(Calendar.MONTH, correctMonth + 2);
                            String endMonth = calendar.getDisplayName(Calendar.MONTH, Calendar.SHORT, Locale.ENGLISH);
                            int year = calendar.get(Calendar.YEAR);
                            String xValue = startMonth + " to " + endMonth + " " + year;
                            if (!xValues.contains(xValue)) {
                                xValues.add(xValue);
                            }

                            calendar.set(Calendar.MONTH, correctMonth);
                            groupedData.putIfAbsent(calendar.getTime(), new HashMap<String, Integer>());
                            groupedData.get(calendar.getTime()).putIfAbsent(data.getAction(), 0);
                            groupedData.get(calendar.getTime()).put(data.getAction(), groupedData.get(calendar.getTime()).get(data.getAction()) + data.getAmount());
                            break;
                        }
                        case 4: {
                            // TO FIX
                            Date logDate = data.getDate();
                            calendar.setTime(logDate);

                            int monthNumber = calendar.get(Calendar.MONTH) + 1; // gennaio è lo 0
                            int correctMonth = (int) Math.floor(monthNumber / 6) + 1;

                            calendar.set(Calendar.MONTH, correctMonth);
                            String startMonth = calendar.getDisplayName(Calendar.MONTH, Calendar.SHORT, Locale.ENGLISH);
                            calendar.set(Calendar.MONTH, correctMonth + 5);
                            String endMonth = calendar.getDisplayName(Calendar.MONTH, Calendar.SHORT, Locale.ENGLISH);
                            int year = calendar.get(Calendar.YEAR);
                            String xValue = startMonth + " to " + endMonth + " " + year;
                            if (!xValues.contains(xValue)) {
                                xValues.add(xValue);
                            }

                            calendar.set(Calendar.MONTH, correctMonth);
                            groupedData.putIfAbsent(calendar.getTime(), new HashMap<String, Integer>());
                            groupedData.get(calendar.getTime()).putIfAbsent(data.getAction(), 0);
                            groupedData.get(calendar.getTime()).put(data.getAction(), groupedData.get(calendar.getTime()).get(data.getAction()) + data.getAmount());
                            break;
                        }
                        case 5: {
                            Date logDate = data.getDate();
                            calendar.setTime(logDate);
                            calendar.set(Calendar.DAY_OF_YEAR, 1);
                            calendar.set(Calendar.YEAR, 2000);
                            String xValue = "Totals";
                            if (!xValues.contains(xValue)) {
                                tableFirstColumnValues.add(calendar.get(Calendar.YEAR));
                                xValues.add(xValue);
                            }
                            groupedData.putIfAbsent(calendar.getTime(), new HashMap<String, Integer>());
                            groupedData.get(calendar.getTime()).put(data.getAction(), data.getAmount());
//                        groupedData.get(calendar.getTime()).putIfAbsent(data.getAction(), 0);
//                        groupedData.get(calendar.getTime()).put(data.getAction(), groupedData.get(calendar.getTime()).get(data.getAction()) + data.getAmount());
                            break;
                        }
                        default:
                            break;
                    }
                }

                for (Date group : groupedData.keySet()) {
                    Map<String, Integer> values = groupedData.get(group);
                    Integer loginValue = values.get("LOGIN_CORRECT");
                    loginValues.add(loginValue == null ? 0 : loginValue);

                    Integer playlistValue = values.get("NEW_PLAYLIST_TV");
                    playlistValues.add(playlistValue == null ? 0 : playlistValue);

                    Integer watchlistValue = values.get("NEW_WATCHLIST");
                    watchlistValues.add(watchlistValue == null ? 0 : watchlistValue);

                    Integer downloadValue = values.get("VIDEO_DOWNLOAD");
                    downloadValues.add(downloadValue == null ? 0 : downloadValue);

                    Integer streamValue = values.get("VIDEO_STREAM");
                    streamValues.add(streamValue == null ? 0 : streamValue);

                    Integer xmlJsonValue = values.get("XML_JSON_DOWNLOAD");
                    xmlJsonValues.add(xmlJsonValue == null ? 0 : xmlJsonValue);

                    Integer exportValue = values.get("EXPORT");
                    exportValues.add(exportValue == null ? 0 : exportValue);

                    int totalValue = (loginValue == null ? 0 : loginValue)
                            + (playlistValue == null ? 0 : playlistValue)
                            + (watchlistValue == null ? 0 : watchlistValue)
                            + (downloadValue == null ? 0 : downloadValue)
                            + (streamValue == null ? 0 : streamValue)
                            + (xmlJsonValue == null ? 0 : xmlJsonValue)
                            + (exportValue == null ? 0 : exportValue);
                    totalValues.add(totalValue);
                }

                StringBuilder userActionsContent = new StringBuilder("[");
                userActionsContent.append("{name: 'Totals', type: 'bar', itemStyle: { normal: { barBorderRadius: [4, 4, 0, 0] } }, data: [");
                userActionsContent.append(StringUtils.join(totalValues, ","));
                userActionsContent.append("]},");

                userActionsContent.append("{name: 'Watchlist', type: 'bar', stack: 'Other', data: [");
                userActionsContent.append(StringUtils.join(watchlistValues, ","));
                userActionsContent.append("]},");

                userActionsContent.append("{name: 'Playlists', type: 'bar', stack: 'Other', data: [");
                userActionsContent.append(StringUtils.join(playlistValues, ","));
                userActionsContent.append("]},");

                userActionsContent.append("{name: 'Exports', type: 'bar', stack: 'Other', data: [");
                userActionsContent.append(StringUtils.join(exportValues, ","));
                userActionsContent.append("]},");

                userActionsContent.append("{name: 'XML/Json', type: 'bar', stack: 'Other', data: [");
                userActionsContent.append(StringUtils.join(xmlJsonValues, ","));
                userActionsContent.append("]},");

                userActionsContent.append("{name: 'Download', type: 'bar', stack: 'Other', data: [");
                userActionsContent.append(StringUtils.join(downloadValues, ","));
                userActionsContent.append("]},");

                userActionsContent.append("{name: 'Stream', type: 'bar', stack: 'Other', data: [");
                userActionsContent.append(StringUtils.join(streamValues, ","));
                userActionsContent.append("]},");

                userActionsContent.append("{name: 'Login', type: 'bar', stack: 'Other', itemStyle: { normal: { barBorderRadius: [4, 4, 0, 0] } }, data: [");
                userActionsContent.append(StringUtils.join(loginValues, ","));
                userActionsContent.append("]},");

                if (!userActionsGrouped.isEmpty()) {
                    userActionsContent.deleteCharAt(userActionsContent.length() - 1);
                }
                userActionsContent.append("]");

                StringBuilder xAxisContent = new StringBuilder("[");
                for (String value : xValues) {
                    xAxisContent.append("'");
                    xAxisContent.append(value);
                    xAxisContent.append("',");
                }
                if (!xValues.isEmpty()) {
                    xAxisContent.deleteCharAt(xAxisContent.length() - 1);
                }
                xAxisContent.append("]");

                Gson gson = new Gson();
                JsonObject jsonObject = new JsonObject();
                jsonObject.addProperty("actionsContent", userActionsContent.toString());
                jsonObject.addProperty("actionsXValues", xAxisContent.toString());

                model.addAttribute("mChartGroupedData", groupedData);
                model.addAttribute("mFirstColumnValues", tableFirstColumnValues);
                model.addAttribute("mChartData", gson.toJson(jsonObject));
            }
        } catch (ParseException ex) {
            GlobalHelper.reportError(ex);
        }

        return pageUserReport;
    }

    @RequestMapping("/translations")
    public String translations(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response) {
        if (!this.initModule(session, model)) {
            return pageRedirect(pageHome);
        }
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        if (!curUser.hasAccess(8)) {
            return pageRedirect(pageHome);
        }

        model.addAttribute("mUUID", UUID.randomUUID().toString());

        return pageTranslations;
    }

    @RequestMapping("/syncLanguageMongo")
    public @ResponseBody
    String syncLanguageMongo(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam("type") Integer type, @RequestParam("basePath") String basePath) {
        if (!this.initModule(session, model)) {
            return pageRedirect(pageHome);
        }
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        if (!curUser.hasAccess(8)) {
            return pageRedirect(pageHome);
        }

        String done = LanguageController.syncronizeMongodb(type, basePath);
        return done;
    }

    @RequestMapping("/getTranslationsData")
    public @ResponseBody
    void getTranslationsData(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam("type") Integer type) {
        if (!this.initModule(session, model)) {
            return;
        }
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        if (!curUser.hasAccess(8)) {
            return;
        }

        String tableData = LanguageController.getTableData(type);

        // Codice per impostare utf-8
        response.setContentType("text/plain;charset=UTF-8");
        try (PrintWriter out = response.getWriter()) {
            out.write(tableData);
        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }
    }

    @RequestMapping(value = "/importTranslationsData", method = RequestMethod.POST)
    public @ResponseBody
    String importTranslationsData(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam("uuid") String randomUUID, @RequestParam("file") MultipartFile file,
            @RequestParam("type") Integer type, @RequestParam("isLastFile") Boolean isLastFile) {
        if (!this.initModule(session, model)) {
            return pageRedirect(pageHome);
        }
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        if (!curUser.hasAccess(8)) {
            return pageRedirect(pageHome);
        }

        String result = "ko";
        if (StringUtils.isNotBlank(randomUUID) && type != null) {
            temporaryTranslations.putIfAbsent(randomUUID, new ArrayList<PropertyInputFile>());
            if (FilenameUtils.getExtension(file.getOriginalFilename()).equalsIgnoreCase("properties")) {
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(file.getInputStream()))) {
                    Properties properties = new Properties();
                    properties.load(reader);
                    String language = FilenameUtils.getBaseName(file.getOriginalFilename()).replace("ApplicationResources_", "").replace("Resources_", "").replace("Bundle_", "");
                    if (type != 0) {
                        // per videomatch se trovo la lingua vuota significa che è l'it
                        if (StringUtils.isBlank(language)) {
                            language = "it";
                        }
                    }
                    PropertyInputFile propertyInput = new PropertyInputFile();
                    propertyInput.setProperty(properties);
                    propertyInput.setLanguage(language);
                    temporaryTranslations.get(randomUUID).add(propertyInput);
                } catch (IOException ex) {
                    GlobalHelper.reportError(ex);
                }
            }

            if (BooleanUtils.isTrue(isLastFile)) {
                result = LanguageController.syncronizeMongodb(type, temporaryTranslations.get(randomUUID));
            } else {
                result = "fileAdded";
            }
        }

        return result;
    }

    @RequestMapping("/saveTranslationsData")
    public @ResponseBody
    String saveTranslationsData(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam("type") Integer type, @RequestParam("json") String json, @RequestParam("modifiedIndexes") List<Integer> modifiedIndexes,
            @RequestParam("modifiedIndexesLabel") String modifiedIndexesLabel) {
        if (!this.initModule(session, model)) {
            return pageRedirect(pageHome);
        }
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        if (!curUser.hasAccess(8)) {
            return pageRedirect(pageHome);
        }

        Map<Integer, List<Integer>> indexChanges = new LinkedHashMap<>();
        if (modifiedIndexes != null && !modifiedIndexes.isEmpty()) {
            for (int i = 0; i < modifiedIndexes.size(); i++) {
                if (i % 2 == 0) {
                    // indice riga
                    indexChanges.putIfAbsent(modifiedIndexes.get(i), new ArrayList<Integer>());
                } else {
                    // indice colonna
                    indexChanges.putIfAbsent(modifiedIndexes.get(i - 1), new ArrayList<Integer>());
                    indexChanges.get(modifiedIndexes.get(i - 1)).add(modifiedIndexes.get(i));
                }
            }
        }

        Map<String, Integer> indexLabels = new HashMap<>();
        if (StringUtils.isNotBlank(modifiedIndexesLabel)) {
            List<String> splitted = Arrays.asList(StringUtils.split(modifiedIndexesLabel, ","));
            if (!splitted.isEmpty()) {
                for (String split : splitted) {
                    List<String> parts = Arrays.asList(StringUtils.split(split, "|"));
                    if (parts.size() == 2) {
                        indexLabels.put(parts.get(1), Integer.valueOf(parts.get(0)));
                    }
                }
            }
        }

        try {
            LanguageController.updateTranslations(curUser, type, json, indexChanges, indexLabels);
        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
            return "ko";
        }
        return "ok";
    }

    @RequestMapping("/downloadTranslationsData")
    public @ResponseBody
    void downloadTranslationsData(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam("type") Integer type) {
        if (!this.initModule(session, model)) {
            return;
        }
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        if (!curUser.hasAccess(8)) {
            return;
        }

        List<Translation> translations = MongoHelper.getTranslations(type);
        List<String> it = new ArrayList<>();
        List<String> en = new ArrayList<>();
        List<String> fr = new ArrayList<>();
        List<String> es = new ArrayList<>();

        for (Translation translation : translations) {
            if (StringUtils.isNotBlank(translation.getKey())) {
                it.add(translation.getKey() + "=" + StringUtils.defaultIfEmpty(translation.getIt(), ""));
                en.add(translation.getKey() + "=" + StringUtils.defaultIfEmpty(translation.getEn(), ""));
                fr.add(translation.getKey() + "=" + StringUtils.defaultIfEmpty(translation.getFr(), ""));
                es.add(translation.getKey() + "=" + StringUtils.defaultIfEmpty(translation.getEs_ES(), ""));
            }
        }

        // Simula la generazione dei file .properties
        List<String> fileNames = new ArrayList<>();
        if (type == 0) {
            // sicstv
            fileNames.add("ApplicationResources_it.properties");
            fileNames.add("ApplicationResources_en.properties");
            fileNames.add("ApplicationResources_fr.properties");
            fileNames.add("ApplicationResources_es.properties");
        } else {
            // videomatch, dynamic...
            fileNames.add("Resources.properties");
            fileNames.add("Resources_en.properties");
            fileNames.add("Resources_fr.properties");
            fileNames.add("Resources_es.properties");
            fileNames.add("Bundle.properties");
            fileNames.add("Resources_en.properties");
            fileNames.add("Resources_fr.properties");
            fileNames.add("Resources_es.properties");
        }

        // Configura la risposta per inviare un file ZIP
        response.setContentType("application/zip");
        response.setHeader("Content-Disposition", "attachment; filename=\"properties.zip\"");

        try (ZipOutputStream zipOut = new ZipOutputStream(response.getOutputStream())) {
            for (String filename : fileNames) {
                zipOut.putNextEntry(new ZipEntry(filename));
                if (StringUtils.contains(filename, "en")) {
                    zipOut.write(StringUtils.join(en, "\n").getBytes());
                } else if (StringUtils.contains(filename, "fr")) {
                    zipOut.write(StringUtils.join(fr, "\n").getBytes());
                } else if (StringUtils.contains(filename, "es")) {
                    zipOut.write(StringUtils.join(es, "\n").getBytes());
                } else {
                    // per videomatch non c'è _it
                    zipOut.write(StringUtils.join(it, "\n").getBytes());
                }
                zipOut.closeEntry();
            }
        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }
    }

    @RequestMapping(value = "/getAWSUploadURLForFixture")
    public @ResponseBody
    String getAWSUploadURLForFixture(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam("fixtureId") Long fixtureId, @RequestParam("fileName") String fileName,
            @RequestParam("contentType") String contentType) {

        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        String presignedURL = AWSHelper.getPresignedUrlForFixture(curUser, fixtureId, fileName, contentType);
        return presignedURL;
    }

    @RequestMapping(value = "/gamesCompleteUpload")
    public @ResponseBody
    String gamesCompleteUpload(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam("fixtureId") Long fixtureId, @RequestParam("fileNames") String fileNames) {
        String result = "ko";
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);

        if (fixtureId != null) {
            List<String> uploadedFileLinks = new ArrayList<>();
            List<String> tmpFileNameList = new ArrayList<>();
            if (StringUtils.isNotBlank(fileNames)) {
                tmpFileNameList = new ArrayList<>(Arrays.asList(StringUtils.split(fileNames, "#")));
                List<String> fixedVideoFileNamesList = new ArrayList<>();
                for (String fileName : tmpFileNameList) {
                    String baseName = FilenameUtils.getBaseName(fileName);
                    String extension = FilenameUtils.getExtension(fileName);
                    baseName = GlobalHelper.removeUnwantedCharacter(baseName);
                    String key = baseName + "." + extension;
                    fixedVideoFileNamesList.add(key);
                }
                tmpFileNameList = fixedVideoFileNamesList;
                uploadedFileLinks = AWSHelper.getUploadFileLinks(curUser, fixtureId, tmpFileNameList);
            }

            if (uploadedFileLinks != null && !uploadedFileLinks.isEmpty()) {
                for (String link : uploadedFileLinks) {
                    mService.saveFixtureAnalystNote(fixtureId, 2, link);
                }
            }
            result = "ok";
        }

        return result;
    }

    @RequestMapping("/userTeamReport")
    public String userTeamReport(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response) {
        if (!this.initModule(session, model)) {
            return pageRedirect(pageHome);
        }
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        if (!curUser.hasAccess(10)) {
            return pageRedirect(pageHome);
        }

        List<UserTeamReport> rows = mService.getUserTeamReports(null);
        Map<Long, String> teamMap = new HashMap<>();
        Map<Long, String> fixtureMap = new HashMap<>();
        Map<String, UserTeamReportWrapper> userGroupedData = new HashMap<>();
        if (rows != null && !rows.isEmpty()) {
            for (UserTeamReport row : rows) {
                if (StringUtils.isNotBlank(row.getEmail())) {
                    if (!userGroupedData.containsKey(row.getEmail())) {
                        UserTeamReportWrapper wrapper = new UserTeamReportWrapper();
                        wrapper.setBaseRow(row);
                        userGroupedData.put(row.getEmail(), wrapper);
                    }

                    UserTeamReportWrapper wrapper = userGroupedData.get(row.getEmail());
                    if (row.getFixtureCompetitionId() != null) {
                        wrapper.getRows().add(row);
                        if (!fixtureMap.containsKey(row.getFixtureId())) {
                            fixtureMap.put(row.getFixtureId(), row.getFixtureHomeTeam() + " - " + row.getFixtureAwayTeam() + " (" + DateHelper.toString(row.getFixtureGameDate()) + ")");
                        }
                        if (!wrapper.getFixtureIds().contains(row.getFixtureId())) {
                            wrapper.getFixtureIds().add(row.getFixtureId());
                        }
                    }
                    if (row.getTeamId() != null) {
                        teamMap.putIfAbsent(row.getTeamId(), row.getTeamName());
                        if (!wrapper.getTeams().containsKey(row.getTeamId())) {
                            Team team = new Team();
                            team.setId(row.getTeamId());
                            team.setName(row.getTeamName());
                            team.setNameEn(row.getTeamName());
                            team.setLogo(row.getTeamLogo());
                            wrapper.getTeams().put(row.getTeamId(), team);
                        }
                    }
                }
            }
        }

        model.addAttribute("mUserData", userGroupedData);
        model.addAttribute("mFixtures", fixtureMap);
        // SORT DELLA MAPPA teamMap
        Map<Long, String> sortedTeamMap = new LinkedHashMap<>();
        List<String> list = new ArrayList<>();
        for (Map.Entry<Long, String> entry : teamMap.entrySet()) {
            list.add(entry.getValue());
        }
        Collections.sort(list, new Comparator<String>() {
            @Override
            public int compare(String str, String str1) {
                return (str).compareTo(str1);
            }
        });
        for (String str : list) {
            for (Entry<Long, String> entry : teamMap.entrySet()) {
                if (entry.getValue().equals(str)) {
                    sortedTeamMap.put(entry.getKey(), str);
                }
            }
        }
        model.addAttribute("mTeams", sortedTeamMap);

        return pageUserTeamReport;
    }

    @RequestMapping("/userTeamReportDetails")
    public String userTeamReportDetails(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam("email") String email) {
        if (!this.initModule(session, model)) {
            return pageRedirect(pageHome);
        }
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        if (!curUser.hasAccess(10)) {
            return pageRedirect(pageHome);
        }

        List<UserTeamReport> rows = mService.getUserTeamReports(email);
        if (rows != null && !rows.isEmpty()) {
            Map<Long, UserTeamReport> assetMap = new HashMap<>();
            for (UserTeamReport row : rows) {
                assetMap.put(row.getAssetId(), row);
            }
            model.addAttribute("mAssets", assetMap);

            Long groupsetId = rows.get(0).getGroupsetId();
            Groupset groupset = null;
            if (groupsetId != null) {
                groupset = mService.getGroupset(groupsetId);
            }
            Long userId = rows.get(0).getUserId();
            model.addAttribute("mUserId", userId);
            Long contactId = rows.get(0).getContactId();
            model.addAttribute("mContactId", contactId);

            List<UserTeamReport> dataRows = new ArrayList<>();
            // lista di competizioni per i team a cui ha accesso
            List<Long> teamIds = new ArrayList<>();
            for (UserTeamReport row : rows) {
                if (row.getTeamId() != null && !teamIds.contains(row.getTeamId())) {
                    teamIds.add(row.getTeamId());
                }
                if (row.getFixtureCompetitionId() != null) {
                    dataRows.add(row);
                }
            }

            model.addAttribute("mAllRows", rows);
            model.addAttribute("mRows", dataRows);
            model.addAttribute("mGroupset", groupset);

            if (!teamIds.isEmpty()) {
                Map<Long, Competition> validCompetitionMap = new HashMap<>();

                List<TeamCompetitions> teamCompetitions = mService.getTeamCompetitions(teamIds);
                if (teamCompetitions != null && !teamCompetitions.isEmpty()) {
                    if (groupset != null && groupset.getCompetitionList() != null && !groupset.getCompetitionList().isEmpty()) {
                        for (TeamCompetitions teamCompetition : teamCompetitions) {
                            for (Competition competition : teamCompetition.getCompetitionList()) {
                                if (groupset.getCompetitionList().contains(competition.getId().toString())) {
                                    competition.setVisible(true);
                                }
                                validCompetitionMap.put(competition.getId(), competition);
                            }
                        }
                    }
                }

                List<Competition> validCompetitions = new ArrayList<>(validCompetitionMap.values());
                Collections.sort(validCompetitions, new Comparator<Competition>() {
                    @Override
                    public int compare(Competition o1, Competition o2) {
                        return o1.getName().compareTo(o2.getName());
                    }
                });

                model.addAttribute("mCompetitions", validCompetitions);
                model.addAttribute("mTeamCompetitions", teamCompetitions);
                model.addAttribute("mTeamIds", StringUtils.join(teamIds, ","));
            }
        } else {
            return pageRedirect(pageUserTeamReport);
        }

        return pageUserTeamReportDetails;
    }

    @RequestMapping("/userPlayerReport")
    public String userPlayerReport(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response) {
        if (!this.initModule(session, model)) {
            return pageRedirect(pageHome);
        }
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        if (!curUser.hasAccess(10)) {
            return pageRedirect(pageHome);
        }

        List<UserPlayerReport> rows = mService.getUserPlayerReports(null);
        Map<Long, String> fixtureMap = new HashMap<>();
        Map<String, UserPlayerReportWrapper> userGroupedData = new HashMap<>();
        if (rows != null && !rows.isEmpty()) {
            for (UserPlayerReport row : rows) {
                if (StringUtils.isNotBlank(row.getEmail())) {
                    if (!userGroupedData.containsKey(row.getEmail())) {
                        UserPlayerReportWrapper wrapper = new UserPlayerReportWrapper();
                        wrapper.setBaseRow(row);
                        userGroupedData.put(row.getEmail(), wrapper);
                    }

                    UserPlayerReportWrapper wrapper = userGroupedData.get(row.getEmail());
                    if (row.getFixtureCompetitionId() != null) {
                        wrapper.getRows().add(row);
                        if (!fixtureMap.containsKey(row.getFixtureId())) {
                            fixtureMap.put(row.getFixtureId(), row.getFixtureHomeTeam() + " - " + row.getFixtureAwayTeam() + " (" + DateHelper.toString(row.getFixtureGameDate()) + ")");
                        }
                        if (!wrapper.getFixtureIds().contains(row.getFixtureId())) {
                            wrapper.getFixtureIds().add(row.getFixtureId());
                        }
                    }
                    if (row.getPlayerId() != null && wrapper.getPlayer() == null) {
                        wrapper.setPlayer(mService.getPlayer(row.getPlayerId()));
                    }
                }
            }
        }

        model.addAttribute("mUserData", userGroupedData);
        model.addAttribute("mFixtures", fixtureMap);

        return pageUserPlayerReport;
    }

    @RequestMapping("/userPlayerReportDetails")
    public String userPlayerReportDetails(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam("email") String email) {
        if (!this.initModule(session, model)) {
            return pageRedirect(pageHome);
        }
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        if (!curUser.hasAccess(10)) {
            return pageRedirect(pageHome);
        }

        List<UserPlayerReport> rows = mService.getUserPlayerReports(email);
        if (rows != null && !rows.isEmpty()) {
            Map<Long, UserPlayerReport> assetMap = new HashMap<>();
            for (UserPlayerReport row : rows) {
                assetMap.put(row.getAssetId(), row);
            }
            model.addAttribute("mAssets", assetMap);

            Long groupsetId = rows.get(0).getGroupsetId();
            Groupset groupset = null;
            if (groupsetId != null) {
                groupset = mService.getGroupset(groupsetId);
            }
            Long userId = rows.get(0).getUserId();
            model.addAttribute("mUserId", userId);
            Long contactId = rows.get(0).getContactId();
            model.addAttribute("mContactId", contactId);

            List<UserPlayerReport> dataRows = new ArrayList<>();
            // lista di competizioni per il player
            List<Long> playerIds = new ArrayList<>();
            for (UserPlayerReport row : rows) {
                if (row.getPlayerId() != null && !playerIds.contains(row.getPlayerId())) {
                    playerIds.add(row.getPlayerId());
                }
                if (row.getFixtureCompetitionId() != null) {
                    dataRows.add(row);
                }
            }

            model.addAttribute("mAllRows", rows);
            model.addAttribute("mRows", dataRows);
            model.addAttribute("mGroupset", groupset);

            if (!playerIds.isEmpty()) {
                Map<Long, Competition> validCompetitionMap = new HashMap<>();

                List<PlayerCompetitions> playerCompetitions = mService.getPlayerCompetitions(playerIds);
                if (playerCompetitions != null && !playerCompetitions.isEmpty()) {
                    if (groupset != null && groupset.getCompetitionList() != null && !groupset.getCompetitionList().isEmpty()) {
                        for (PlayerCompetitions playerCompetition : playerCompetitions) {
                            for (Competition competition : playerCompetition.getCompetitionList()) {
                                if (groupset.getCompetitionList().contains(competition.getId().toString())) {
                                    competition.setVisible(true);
                                }
                                validCompetitionMap.put(competition.getId(), competition);
                            }
                        }
                    }
                }

                List<Competition> validCompetitions = new ArrayList<>(validCompetitionMap.values());
                Collections.sort(validCompetitions, new Comparator<Competition>() {
                    @Override
                    public int compare(Competition o1, Competition o2) {
                        return o1.getName().compareTo(o2.getName());
                    }
                });

                model.addAttribute("mCompetitions", validCompetitions);
                model.addAttribute("mPlayerCompetitions", playerCompetitions);
                model.addAttribute("mPlayerIds", StringUtils.join(playerIds, ","));
            }
        } else {
            return pageRedirect(pageUserPlayerReport);
        }

        return pageUserPlayerReportDetails;
    }

    @RequestMapping("/searchFixtureAnalyzedByCompetitionId")
    public @ResponseBody
    void searchFixtureAnalyzedByCompetitionId(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam(value = "competitionId") Long competitionId, @RequestParam(value = "teamIds", required = false) String teamIds,
            @RequestParam(value = "playerIds", required = false) String playerIds) {
        JsonObject result = new JsonObject();

        Gson gson = new GsonBuilder()
                .setDateFormat("dd-MM-yyyy").create();
        if (competitionId != null) {
            if (StringUtils.isNotBlank(teamIds) || StringUtils.isNotBlank(playerIds)) {
                List<Fixture> fixtures = mService.searchFixtureAnalyzedByCompetitionId(competitionId, teamIds, playerIds);
                if (fixtures != null && !fixtures.isEmpty()) {
                    JsonArray datas = new JsonArray();
                    for (Fixture fixture : fixtures) {
                        datas.add(gson.toJsonTree(fixture));
                    }
                    result.add("datas", datas);
                }
            }
        }

        // Codice per impostare utf-8
        response.setContentType("application/json;charset=UTF-8");
        try (PrintWriter out = response.getWriter()) {
            out.write(result.toString());
        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }
    }

    @RequestMapping("/sendFixtureReport")
    public @ResponseBody
    String sendFixtureReport(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam(value = "fixtureIds") String fixtureIds, @RequestParam(value = "what") Integer what,
            @RequestParam(value = "contactId", required = false) Long contactId, @RequestParam(value = "userId", required = false) Long userId,
            @RequestParam(value = "teamIds", required = false) String teamIds, @RequestParam(value = "playerIds", required = false) String playerIds) {
        String result = "ko";

        if (StringUtils.isNotBlank(fixtureIds) && what != null) {
            /*
                WHAT = 1 -> Rimando una specifica partita
                WHAT = 2 -> Mando per la prima volta una o più partite
             */

            boolean validRequest = true;
            if (what == 2) {
                if (contactId == null || userId == null || (StringUtils.isBlank(teamIds) && StringUtils.isBlank(playerIds))) {
                    validRequest = false;
                }
            }

            if (validRequest) {
                mService.sendFixtureReport(what, fixtureIds, userId, contactId, teamIds, playerIds);
                result = "ok";
            }
        }

        return result;
    }

    @RequestMapping("/removeFixtureFile")
    public @ResponseBody
    String removeFixtureFile(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam(value = "fixtureId") Long fixtureId, @RequestParam(value = "filePath") String filePath) {
        String result = "ko";

        if (fixtureId != null && StringUtils.isNotBlank(filePath)) {
            mService.removeFixtureFile(fixtureId, filePath);
            result = "ok";
        }

        return result;
    }

    @RequestMapping("/reaperReport")
    public String reaperReport(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response) {
        if (!this.initModule(session, model)) {
            return pageRedirect(pageHome);
        }
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        if (!curUser.hasAccess(14)) {
            return pageRedirect(pageHome);
        }

        List<User> reapers = mService.getReapers();
        model.addAttribute("mReapers", reapers);

        List<Competition> competitions = mService.getAllCompetitions();
        model.addAttribute("mCompetitions", competitions);

        List<String> lastThreeMonths = new ArrayList<>();
        LocalDate currentDate = LocalDate.now();
        for (int i = 0; i < 3; i++) {
            // Subtract months to get the last three months
            LocalDate month = currentDate.minusMonths(i);
            // Get the full month name
            String monthName = month.getMonth().getDisplayName(TextStyle.FULL, Locale.ENGLISH);
            lastThreeMonths.add(monthName);
        }
        model.addAttribute("mMonths", lastThreeMonths);

        return pageReaperReport;
    }

    @RequestMapping("/reaperReportData")
    public String reaperReportData(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam("reaperId") Long reaperId, @RequestParam(value = "competitionIds", required = false) String competitionIds, @RequestParam("option") Integer option) {
        if (!this.initModule(session, model)) {
            return "";
        }
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        if (!curUser.hasAccess(14)) {
            return "";
        }

        if (option != null) {
            // devo prendere i dati degli ultimi 3 mesi per calcolare penalità e bonus
            Date to = new Date();
            if (option == 2) {
                Calendar calendar = Calendar.getInstance();
                calendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH) - 1);
                calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
                calendar.set(Calendar.HOUR_OF_DAY, 23);
                calendar.set(Calendar.MINUTE, 59);
                calendar.set(Calendar.SECOND, 59);
                calendar.set(Calendar.MILLISECOND, 999);
                to = calendar.getTime();
            } else if (option == 3) {
                Calendar calendar = Calendar.getInstance();
                calendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH) - 2);
                calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
                calendar.set(Calendar.HOUR_OF_DAY, 23);
                calendar.set(Calendar.MINUTE, 59);
                calendar.set(Calendar.SECOND, 59);
                calendar.set(Calendar.MILLISECOND, 999);
                to = calendar.getTime();
            }
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(to);
//            calendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH) - 1);
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            Date from = calendar.getTime();

            List<Long> reaperIds = new ArrayList<>();
            List<Long> competitions = new ArrayList<>();
            if (reaperId != null) {
                reaperIds.add(reaperId);
            }
            if (StringUtils.isNotBlank(competitionIds)) {
                for (String competitionId : StringUtils.split(competitionIds, "|")) {
                    competitions.add(Long.valueOf(competitionId));
                }
            }
            List<Fixture> uploadedFixtures = mService.getReaperUploadedFixture(reaperIds, competitions, from, to);
            if (!uploadedFixtures.isEmpty()) {
                model.addAttribute("mTotalGames", uploadedFixtures.size());
            }
            model.addAttribute("mGames", uploadedFixtures);

            Map<Long, ReaperDataWrapper> reaperTotals = new HashMap<>();
            if (!uploadedFixtures.isEmpty()) {
                for (Fixture fixture : uploadedFixtures) {
                    if (fixture.getUploaderId() != null) {
                        if (!reaperTotals.containsKey(fixture.getUploaderId())) {
                            ReaperDataWrapper wrapper = new ReaperDataWrapper();
                            wrapper.setFixtures(new ArrayList<Fixture>());
                            wrapper.setReaper(fixture.getReaper());
                            reaperTotals.put(fixture.getUploaderId(), wrapper);
                        }

                        ReaperDataWrapper wrapper = reaperTotals.get(fixture.getUploaderId());
                        wrapper.getFixtures().add(fixture);
                    }
                }
            }
            List<ReaperDataWrapper> reaperTotalList = new ArrayList<>(reaperTotals.values());
            Collections.sort(reaperTotalList, new Comparator<ReaperDataWrapper>() {
                @Override
                public int compare(ReaperDataWrapper o1, ReaperDataWrapper o2) {
                    return Integer.valueOf(o2.getFixtures().size()).compareTo(o1.getFixtures().size());
                }
            });
            model.addAttribute("mReaperTotals", reaperTotalList);

            return pageReaperReportData;
        } else {
            return "";
        }
    }
}
