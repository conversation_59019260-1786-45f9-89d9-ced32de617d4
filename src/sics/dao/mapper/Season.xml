<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">
<mapper namespace="Season">

    <resultMap id="resultMapSeason" type="Season">
        <id     property="id"           column="season_id"/>
        <result property="name"		column="season_name" />
        <result property="visible"	column="season_visible" />
    </resultMap>

    <sql id="select">
        SELECT season.id season_id, season.name season_name, season.visible season_visible
        FROM season
    </sql>

    <select id="getSeasonsVisible" resultMap="resultMapSeason">
        <include refid="select"/>
        WHERE season.visible = 1
        ORDER BY season.name DESC 
    </select>
</mapper>